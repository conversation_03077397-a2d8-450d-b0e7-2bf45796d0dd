'use client';

import { useCreateQuoteTemplate } from '@/apis/quotes/quote-templates.api';
import { IQuoteTemplateDetail } from '@/apis/quotes/quote-templates.type';
import { KEYS_TO_QUOTE_TEMPLATE } from '@/constants/key-convert';
import { ROUTES } from '@/lib/routes';
import { convertFormValueToPayload } from '@/utils/convert-data';
import { showToastError, showToastSuccess } from '@/utils/toast-message';
import { useRouter } from 'next/navigation';
import QuotationForm from '../../_components/QuotationForm';

const CreateQuotationForm = () => {
    const router = useRouter();

    const { mutate: createQuoteTemplate } = useCreateQuoteTemplate({
        onSuccess: () => {
            showToastSuccess({
                title: 'Tạo mới mẫu báo giá thành công',
                message:
                    'Thông tin mẫu báo giá đã được tạo mới thành công trong hệ thống.',
            });
            router.push(
                ROUTES.PRODUCT_MANAGEMENT.QUOTES.INDEX + '?tab=mau-bao-gia',
            );
        },
        onError: (error) => {
            showToastError({
                title: 'Tạo mới mẫu báo giá thất bại',
                message: error.detail,
            });
        },
    });

    const handleCreateQuoteTemplate = (data: IQuoteTemplateDetail) => {
        const payload = convertFormValueToPayload(data, KEYS_TO_QUOTE_TEMPLATE);
        payload.quotesType = 1;

        createQuoteTemplate(payload);
    };

    const handleCancel = () => {
        router.push(
            ROUTES.PRODUCT_MANAGEMENT.QUOTES.INDEX + '?tab=mau-bao-gia',
        );
    };

    return (
        <QuotationForm
            onSubmit={handleCreateQuoteTemplate}
            onCancel={handleCancel}
        />
    );
};

export default CreateQuotationForm;
