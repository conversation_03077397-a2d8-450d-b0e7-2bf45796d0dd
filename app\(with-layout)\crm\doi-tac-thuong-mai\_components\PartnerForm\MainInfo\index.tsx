'use client';
import Avatar from '@/components/common/Avatar';
import CollapseApp from '@/components/common/CollapseApp';
import FormController from '@/components/common/FormController';
import { Col, FormGroup, Label, Row } from 'reactstrap';

const MainInfo = () => {
    return (
        <CollapseApp title='THÔNG TIN CHÍNH'>
            <Row className='g-3 justify-content-around'>
                <Col md='11'>
                    <FormController
                        controlType='textInput'
                        name='addTradePartnerDto.name'
                        label='Tên đối tác thương mại'
                        placeholder='Nhập tên đối tác thương mại'
                        required={true}
                    />
                </Col>
                <Col md='11'>
                    <FormController
                        controlType='textInput'
                        name='addTradePartnerDto.shortName'
                        label='Tên rút gọn'
                        placeholder='Nhập rút gọn của đối tác thương mại'
                    />
                </Col>
                <Col md='5'>
                    <FormController
                        controlType='textInput'
                        name='addTradePartnerDto.email'
                        label='Email'
                        placeholder='Nhập địa chỉ email của đối tác thương mại'
                        required={true}
                    />
                </Col>
                <Col md='5'>
                    <FormController
                        controlType='textInput'
                        name='addTradePartnerDto.phoneNumber'
                        label='Số điện thoại'
                        placeholder='Nhập số điện thoại của đối tác thương mại'
                        required={true}
                    />
                </Col>
                <Col md='5'>
                    <FormController
                        controlType='textInput'
                        name='addTradePartnerDto.taxCode'
                        label='Mã số thuế'
                        placeholder='Nhập mã số thuế của đối tác thương mại'
                    />
                </Col>
                <Col md='5'>
                    <FormGroup>
                        <Label>
                            <strong>Ảnh đại diện</strong>
                        </Label>
                        <div>
                            <Avatar name='addTradePartnerDto.name' />
                        </div>
                    </FormGroup>
                </Col>
                <Col md='11'>
                    <FormController
                        controlType='textInput'
                        name='addTradePartnerDto.address'
                        label='Địa chỉ'
                        placeholder='Nhập địa chỉ của đối tác thương mại'
                    />
                </Col>
            </Row>
        </CollapseApp>
    );
};

export default MainInfo;
