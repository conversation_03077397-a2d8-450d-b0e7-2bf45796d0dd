'use client';

import useGetOptionUserCreate from '@/hooks/useGetOptionUserCreate';
import { Option } from '@/types/app.type';
import {
    CheckIcon,
    Combobox,
    Group,
    Pill,
    PillsInput,
    useCombobox,
} from '@mantine/core';

import { IconSelector } from '@tabler/icons-react';
import React, { useEffect, useMemo, useState } from 'react';
import { useFormContext } from 'react-hook-form';

interface Props {
    name: string;
    label?: string;
    placeholder?: string;
    style?: React.CSSProperties;
    searchInit?: string;
    readOnly?: boolean;
    description?: string;
    required?: boolean;
}

const ComboboxSelectUserCreate = (props: Props) => {
    const {
        name,
        label,
        placeholder = 'Người tạo',
        style = {},
        searchInit,
        readOnly,
        description,
        required,
    } = props;

    const stylePillsInput: React.CSSProperties = { width: '250px', ...style };

    const combobox = useCombobox({
        onDropdownClose: () => combobox.resetSelectedOption(),
        onDropdownOpen: () => combobox.updateSelectedOptionIndex('active'),
    });

    const [search, setSearch] = useState('');

    const methods = useFormContext();
    const { watch, setValue } = methods;
    const value = watch(name) ?? '';

    const users = useGetOptionUserCreate();

    const handleValueSelect = (val: string) => {
        setValue(name, val);
        combobox.closeDropdown();
    };

    const valueSelected = useMemo(() => {
        const optionSelected = users.find((user) => user.value === value);

        if (!optionSelected) {
            return null;
        }

        return (
            <Pill
                key={value}
                withRemoveButton={!readOnly}
                onRemove={() => {
                    if (!readOnly) {
                        setValue(name, '');
                    }
                }}
            >
                {optionSelected.label}
            </Pill>
        );
    }, [users, name, setValue, value, readOnly]);

    const options = users
        .filter((user: Option) =>
            user.label.toLowerCase().includes(search.trim().toLowerCase()),
        )
        .map((user: Option) => (
            <Combobox.Option
                value={user.value}
                key={user.value}
                active={value === user.value}
            >
                <Group gap='sm'>
                    {value === user.value && <CheckIcon size={12} />}
                    <span>{user.label}</span>
                </Group>
            </Combobox.Option>
        ));

    useEffect(() => {
        if (searchInit) {
            setSearch(searchInit);
        }
    }, [searchInit]);

    return (
        <Combobox
            store={combobox}
            onOptionSubmit={handleValueSelect}
            readOnly={readOnly}
        >
            <Combobox.DropdownTarget>
                <PillsInput
                    style={stylePillsInput}
                    onClick={() => {
                        if (!readOnly) {
                            combobox.openDropdown();
                        }
                    }}
                    label={label}
                    description={description}
                    required={required}
                    rightSection={<IconSelector size={17} stroke={1.5} />}
                >
                    <Pill.Group>
                        {valueSelected ? (
                            valueSelected
                        ) : (
                            <Combobox.EventsTarget>
                                <PillsInput.Field
                                    value={search}
                                    readOnly={readOnly}
                                    placeholder={!value ? placeholder : ''}
                                    onChange={(event) => {
                                        combobox.updateSelectedOptionIndex();
                                        setSearch(event.currentTarget.value);
                                    }}
                                    onKeyDown={() => {
                                        setValue(name, '');
                                    }}
                                    onFocus={() => {
                                        if (!readOnly) {
                                            combobox.openDropdown();
                                        }
                                    }}
                                    onBlur={() => {
                                        combobox.closeDropdown();
                                        setSearch(value || '');
                                    }}
                                />
                            </Combobox.EventsTarget>
                        )}
                    </Pill.Group>
                </PillsInput>
            </Combobox.DropdownTarget>

            <Combobox.Dropdown>
                <Combobox.Options mah={200} style={{ overflowY: 'auto' }}>
                    {options.length > 0 ? (
                        options
                    ) : (
                        <Combobox.Empty>
                            Không tìm thấy người tạo...
                        </Combobox.Empty>
                    )}
                </Combobox.Options>
            </Combobox.Dropdown>
        </Combobox>
    );
};

export default ComboboxSelectUserCreate;
