import { useFormContext } from 'react-hook-form';
import CollapseApp from '@/components/common/CollapseApp';
import FormController from '@/components/common/FormController';
import { Row, Col } from 'reactstrap';
import { useEffect } from 'react';
import {
    deliveryType,
    documentIncludedType,
    regionDeliveryType,
} from '@/constants/sharedData/sharedData';
import useGetOptionsAddressString from '@/hooks/useGetOptionsAddressString';

const Delivery = () => {
    const { watch, setValue } = useFormContext();
    const provinceName = watch('delivery.city');
    const districtName = watch('delivery.district');
    const { province, district, ward } = useGetOptionsAddressString({
        provinceName,
        districtName,
    });

    // Set default country value in useEffect to avoid setState during render
    useEffect(() => {
        setValue('delivery.country', 'Việt Nam');
    }, [setValue]);
    return (
        <CollapseApp title='GIAO HÀNG'>
            <div style={{ padding: '20px 40px 20px 40px' }}>
                <Row>
                    <Col
                        md={2}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <FormController
                            controlType='select'
                            name='delivery.deliveryType'
                            label='Loại giao hàng'
                            data={deliveryType}
                            required
                        />
                    </Col>
                    <Col
                        md={4}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <FormController
                            controlType='textInput'
                            name='delivery.deliveryWeek'
                            label='Ngày giao dự kiến (tuần)'
                            placeholder='Nhập số tuần dự kiến hàng được giao'
                            required
                            type='number'
                        />
                    </Col>
                    <Col
                        md={3}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <FormController
                            controlType='select'
                            name='delivery.regionDeliveryType'
                            label='Địa chỉ'
                            data={regionDeliveryType}
                            required
                        />
                    </Col>
                    <Col
                        md={3}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <FormController
                            controlType='select'
                            name='delivery.country'
                            label='Quốc gia'
                            data={[{ value: 'Việt Nam', label: 'Việt Nam' }]}
                            placeholder='Chọn quốc gia'
                            required
                        />
                    </Col>
                    <Col
                        md={6}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <FormController
                            controlType='select'
                            name='delivery.city'
                            label='Tỉnh/Thành phố'
                            data={province}
                            placeholder='Chọn tỉnh/thành phố'
                            required
                        />
                    </Col>
                    <Col
                        md={6}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <FormController
                            controlType='select'
                            name='delivery.district'
                            label='Quận/Huyện'
                            data={district}
                            placeholder='Chọn quận/huyện'
                            required
                        />
                    </Col>
                    <Col
                        md={6}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <FormController
                            controlType='select'
                            name='delivery.ward'
                            label='Phường/Xã'
                            data={ward}
                            placeholder='Chọn phường/xã'
                            required
                        />
                    </Col>
                    <Col
                        md={6}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <FormController
                            controlType='textInput'
                            name='delivery.addressDetail'
                            label='Địa chỉ chi tiết'
                            placeholder='Nhập địa chỉ chi tiết'
                            required
                        />
                    </Col>
                    <Col
                        md={6}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <FormController
                            controlType='select'
                            name='delivery.documentIncludedType'
                            label='Tài liệu bao gồm'
                            data={documentIncludedType}
                            required
                        />
                    </Col>
                    <Col
                        md={6}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <FormController
                            controlType='numberInput'
                            name='delivery.documentQuantity'
                            label='Số lượng chứng từ'
                            required
                        />
                    </Col>
                </Row>
            </div>
        </CollapseApp>
    );
};
export default Delivery;
