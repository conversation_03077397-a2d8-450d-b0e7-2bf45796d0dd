'use client';

import { useGetDealsByPipelineStageSort } from '@/apis/opportunity/opportunity.api';
import { IDealResponse } from '@/apis/opportunity/opportunity.type';
import { Option } from '@/types/app.type';
import {
    CheckIcon,
    Combobox,
    Group,
    Pill,
    PillsInput,
    useCombobox,
} from '@mantine/core';
import { useDebouncedValue } from '@mantine/hooks';
import { IconSelector } from '@tabler/icons-react';
import React, { useMemo, useState } from 'react';
import { useFormContext } from 'react-hook-form';

interface Props {
    name: string;
    label?: string;
    placeholder?: string;
    sorts?: string[];
    style?: React.CSSProperties;
    required?: boolean;
    maxHeight?: number;
}

const ComboboxSelectDealControl = (props: Props) => {
    const {
        name,
        label,
        placeholder = 'Chọn cơ hội',
        sorts,
        style = {},
        required = false,
        maxHeight = 200,
    } = props;

    const stylePillsInput: React.CSSProperties = { width: '250px', ...style };

    const combobox = useCombobox({
        onDropdownClose: () => combobox.resetSelectedOption(),
        onDropdownOpen: () => combobox.updateSelectedOptionIndex('active'),
    });

    const [search, setSearch] = useState('');

    const methods = useFormContext();
    const { watch, setValue } = methods;
    const value = watch(name) ?? '';

    const [debouncedSearch] = useDebouncedValue(search, 1500);

    const { data, isLoading } = useGetDealsByPipelineStageSort({
        Name: debouncedSearch,
        Sorts: sorts,
        PageNumber: 1,
        PageSize: 20,
    });

    const deals = useMemo<Option[]>(() => {
        if (data?.items) {
            return data?.items.map(
                (deal: Pick<IDealResponse, 'dealId' | 'title'>) => ({
                    value: deal.dealId,
                    label: deal.title,
                }),
            );
        }

        return [];
    }, [data]);

    const handleValueSelect = (val: string) => {
        setValue(name, val);
        combobox.closeDropdown();
    };

    const valueSelected = useMemo(() => {
        const optionSelected = deals.find((deal) => deal.value === value);

        if (!optionSelected) {
            return null;
        }

        return (
            <Pill
                key={value}
                withRemoveButton
                onRemove={() => setValue(name, '')}
            >
                {optionSelected.label}
            </Pill>
        );
    }, [deals, name, setValue, value]);

    const options = deals
        .filter((deal: Option) =>
            deal.label.toLowerCase().includes(search.trim().toLowerCase()),
        )
        .map((deal: Option) => (
            <Combobox.Option
                value={deal.value}
                key={deal.value}
                active={value === deal.value}
            >
                <Group gap='sm'>
                    {value === deal.value && <CheckIcon size={12} />}
                    <span>{deal.label}</span>
                </Group>
            </Combobox.Option>
        ));

    return (
        <Combobox store={combobox} onOptionSubmit={handleValueSelect}>
            <Combobox.DropdownTarget>
                <PillsInput
                    style={stylePillsInput}
                    onClick={() => combobox.openDropdown()}
                    label={label}
                    required={required}
                    rightSection={<IconSelector size={17} stroke={1.5} />}
                >
                    <Pill.Group>
                        {valueSelected ? (
                            valueSelected
                        ) : (
                            <Combobox.EventsTarget>
                                <PillsInput.Field
                                    value={search}
                                    placeholder={!value ? placeholder : ''}
                                    onChange={(event) => {
                                        combobox.updateSelectedOptionIndex();
                                        setSearch(event.currentTarget.value);
                                    }}
                                    onKeyDown={() => {
                                        setValue(name, '');
                                    }}
                                    onFocus={() => combobox.openDropdown()}
                                    onBlur={() => {
                                        combobox.closeDropdown();
                                        setSearch(value || '');
                                    }}
                                />
                            </Combobox.EventsTarget>
                        )}
                    </Pill.Group>
                </PillsInput>
            </Combobox.DropdownTarget>

            <Combobox.Dropdown>
                <Combobox.Options mah={maxHeight} style={{ overflowY: 'auto' }}>
                    {options.length > 0 ? (
                        options
                    ) : (
                        <Combobox.Empty>
                            {isLoading
                                ? 'Đang tìm kiếm cơ hội...'
                                : 'Không tìm thấy cơ hội...'}
                        </Combobox.Empty>
                    )}
                </Combobox.Options>
            </Combobox.Dropdown>
        </Combobox>
    );
};

export default ComboboxSelectDealControl;
