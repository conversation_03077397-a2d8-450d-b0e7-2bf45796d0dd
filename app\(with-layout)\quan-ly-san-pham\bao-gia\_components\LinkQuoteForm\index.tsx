'use client';

import { useGetInformationDisplayDeal } from '@/apis/quotes/quotes.api';
import { IQuotePayload } from '@/apis/quotes/quotes.type';
import { DealType } from '@/app/(with-layout)/crm/co-hoi-ban-hang/types';
import CollapseApp from '@/components/common/CollapseApp';
import FormController from '@/components/common/FormController';
import ComboboxSelectCumstomerControl from '@/components/common/FormController/ComboboxSelectCumstomerControl';
import ComboboxSelectDealControl from '@/components/common/FormController/ComboboxSelectDealControl';
import ComboboxSelectPartnerControl from '@/components/common/FormController/ComboboxSelectPartnerControl';
import ComboboxSelectQuoteTemplateControl from '@/components/common/FormController/ComboboxSelectQuoteTemplateControl';
import { IconCalendar } from '@tabler/icons-react';
import { useEffect, useMemo } from 'react';
import { FormProvider, useForm, useWatch } from 'react-hook-form';
import { Button, Card, Col, Input, Row } from 'reactstrap';
import TableInfoProduct from '../TableInfoProduct';

interface LinkQuoteFormProps {
    initValue?: IQuotePayload;
    onSubmit: (data: IQuotePayload) => void;
    onCancel: () => void;
    mode?: 'create' | 'edit';
    isLoading?: boolean;
}

const LinkQuoteForm = (props: LinkQuoteFormProps) => {
    const { initValue, onSubmit, onCancel, isLoading } = props;

    const methods = useForm<IQuotePayload>({
        values: initValue ?? ({} as IQuotePayload),
    });

    const { handleSubmit } = methods;

    const handleFormSubmit = (data: IQuotePayload) => {
        onSubmit(data);
    };

    const { setValue, control } = methods;

    const [dealId, requiresApproval] = useWatch({
        control,
        name: ['dealId', 'requiresApproval'],
    });

    const { data: dealDetail } = useGetInformationDisplayDeal(
        {
            id: dealId,
        },
        {
            enabled: !!dealId,
        },
    );

    const {
        companyName,
        companyId,
        tradePartnerId,
        tradePartnerName,
        dealType,
        items,
    } = useMemo(() => {
        return {
            companyName: dealDetail?.companyName ?? '',
            companyId: dealDetail?.companyId ?? '',
            tradePartnerId: dealDetail?.tradePartnerId ?? '',
            tradePartnerName: dealDetail?.tradePartnerName ?? '',
            dealType: dealDetail?.dealType,
            items: dealDetail?.products ?? [],
        };
    }, [dealDetail]);

    useEffect(() => {
        if (companyId) {
            setValue('customer', companyId);
        }
    }, [companyId, setValue]);

    useEffect(() => {
        if (tradePartnerId) {
            setValue('partner', tradePartnerId);
        }
    }, [tradePartnerId, setValue]);

    useEffect(() => {
        setValue('items', items);
    }, [items, setValue]);

    return (
        <FormProvider {...methods}>
            <Card
                style={{ padding: '20px 40px 20px 40px' }}
                className='d-flex flex-column gap-3'
            >
                <CollapseApp title='THÔNG TIN BÁO GIÁ'>
                    <Row className='g-3 justify-content-around'>
                        <Col md='5'>
                            <FormController
                                controlType='textInput'
                                name='name'
                                label='Tên báo giá'
                                placeholder='Nhập tên báo giá...'
                                required={true}
                            />
                        </Col>
                        <Col md='5'>
                            <ComboboxSelectQuoteTemplateControl
                                name='quoteTemplate'
                                label='Mẫu báo giá'
                                placeholder='Chọn mẫu báo giá...'
                                required={true}
                                style={{ width: '100%' }}
                            />
                        </Col>
                        <Col
                            md='11'
                            className='d-flex align-items-center gap-2'
                        >
                            <Input
                                type='checkbox'
                                style={{ width: '15px', height: '15px' }}
                                onChange={(event) => {
                                    const value = event.target
                                        .checked as boolean;

                                    setValue('requiresApproval', value);
                                }}
                                checked={!!requiresApproval}
                            />{' '}
                            <p className='mb-0' style={{ fontSize: '14px' }}>
                                Báo giá có con dấu
                            </p>
                        </Col>
                        <Col md='11'>
                            <FormController
                                controlType='textarea'
                                name='description'
                                label='Mô tả'
                                placeholder='Nhập mô tả báo giá...'
                            />
                        </Col>
                        <Col md='11'>
                            <ComboboxSelectDealControl
                                name='dealId'
                                label='Cơ hội'
                                placeholder='Chọn cơ hội'
                                required={true}
                                style={{ width: '100%' }}
                                maxHeight={180}
                            />
                        </Col>
                        <Col md='5'>
                            <ComboboxSelectCumstomerControl
                                name='customer'
                                label='Khách hàng'
                                placeholder='Khách hàng'
                                style={{ width: '100%' }}
                                readOnly={true}
                                description='Khách hàng sẽ được tự động lấy từ cơ hội'
                                searchInit={companyName}
                            />
                        </Col>
                        <Col md='5'>
                            <ComboboxSelectPartnerControl
                                name='partner'
                                label='Đối tác thương mại'
                                placeholder='Đối tác thương mại'
                                style={{ width: '100%' }}
                                readOnly={true}
                                description='Đối tác thương mại sẽ được tự động lấy từ cơ hội'
                                searchInit={tradePartnerName}
                            />
                        </Col>
                        <Col md='5'>
                            <FormController
                                controlType='dateTimePicker'
                                name='expectedEndDate'
                                label='Hiệu lực báo giá'
                                placeholder='Chọn ngày hết hiệu lực của báo giá...'
                                required={true}
                                clearable
                                style={{ width: '100%' }}
                                leftSection={
                                    <IconCalendar size={18} stroke={1.5} />
                                }
                            />
                        </Col>
                        <Col md='5'></Col>
                    </Row>
                </CollapseApp>

                <CollapseApp title='NỘI DUNG BÁO GIÁ'>
                    <Row className='g-3 justify-content-around'>
                        <Col md='5'>
                            <FormController
                                controlType='numberInput'
                                name='exchangeRate'
                                label='Exchange rate'
                                description='Tỷ giá sẽ áp dụng với tất cả các sản phẩm'
                                placeholder='Nhập tỷ giá hối đoái...'
                                thousandSeparator=','
                                decimalSeparator='.'
                                decimalScale={2}
                                step={0.01}
                                suffix=' VND = 1 USD'
                                required={true}
                            />
                        </Col>
                        <Col md='5'></Col>
                        <Col md='11'>
                            <TableInfoProduct
                                dealType={dealType ?? DealType.Cyber}
                            />
                        </Col>
                    </Row>
                </CollapseApp>

                <Row className='g-3 justify-content-around'>
                    <Col md='11'>
                        <div className='d-flex justify-content-between mt-4'>
                            <Button
                                color='danger'
                                outline
                                onClick={onCancel}
                                disabled={isLoading}
                            >
                                Hủy
                            </Button>
                            <div className='d-flex gap-3'>
                                <Button color='success' outline>
                                    Xem trước
                                </Button>
                                <Button
                                    color='success'
                                    onClick={handleSubmit(handleFormSubmit)}
                                    isLoading={true}
                                >
                                    Tạo mới
                                </Button>
                            </div>
                        </div>
                    </Col>
                </Row>
            </Card>
        </FormProvider>
    );
};

export default LinkQuoteForm;
