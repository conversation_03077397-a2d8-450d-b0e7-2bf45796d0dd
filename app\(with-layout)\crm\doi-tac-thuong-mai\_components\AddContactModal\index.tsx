import { useCreateContact } from '@/apis/contact/contact.api';
import { IContact } from '@/apis/contact/contact.type';
import FormController from '@/components/common/FormController';
import { honorifics } from '@/constants/sharedData/sharedData';
import useGetOptionsDepartments from '@/hooks/useGetOptionsDepartments';
import useGetOptionsPosition from '@/hooks/useGetOptionsPosition';
import { showToastSuccess } from '@/utils/toast-message';
import React from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { toast } from 'react-toastify';
import {
    But<PERSON>,
    Col,
    Modal,
    Modal<PERSON>,
    <PERSON>dal<PERSON>ooter,
    ModalHeader,
    Row,
} from 'reactstrap';

interface AddContactModalProps {
    isOpen: boolean;
    toggle: () => void;
    onSuccess?: () => void;
}

const AddContactModal: React.FC<AddContactModalProps> = ({
    isOpen,
    toggle,
    onSuccess,
}) => {
    const methods = useForm<IContact>({
        defaultValues: {
            honorific: undefined,
            name: '',
            email: '',
            phoneNumber: '',
            departmentId: '',
            positionId: '',
            roleName: '',
        },
    });

    const { mutate: createContact } = useCreateContact({
        onSuccess: () => {
            showToastSuccess({
                title: 'Tạo mới cá nhân thành công',
                message:
                    'Thông tin cá nhân đã được tạo thành công trong hệ thống.',
            });

            methods.reset();
            toggle();
            onSuccess?.();
        },
        onError: () => {
            toast.error('Có lỗi xảy ra khi tạo mới cá nhân');
        },
    });

    const handleSubmit = methods.handleSubmit((data: IContact) => {
        data.honorific = Number(data.honorific);
        createContact(data);
    });

    const { departments } = useGetOptionsDepartments();
    const { positions } = useGetOptionsPosition();

    const formattedDepartments = departments.map((item) => ({
        ...item,
        value: item.value.toString(),
    }));

    const formattedPositions = positions.map((item) => ({
        ...item,
        value: item.value.toString(),
    }));

    return (
        <Modal isOpen={isOpen} toggle={toggle}>
            <FormProvider {...methods}>
                <ModalHeader toggle={toggle}>Thêm cá nhân mới</ModalHeader>
                <ModalBody>
                    <Row className='g-3'>
                        <Col md='12'>
                            <FormController
                                controlType='select'
                                name='honorific'
                                label='Xưng hô'
                                data={honorifics}
                                placeholder='Chọn cách xưng hô'
                            />
                        </Col>

                        <Col md='12'>
                            <FormController
                                controlType='textInput'
                                name='name'
                                label='Họ và tên'
                                placeholder='Nhập họ và tên cá nhân'
                                required={true}
                            />
                        </Col>

                        <Col md='12'>
                            <FormController
                                controlType='textInput'
                                name='email'
                                label='Email'
                                placeholder='Nhập email cá nhân'
                                required={true}
                            />
                        </Col>

                        <Col md='12'>
                            <FormController
                                controlType='textInput'
                                name='phoneNumber'
                                label='Số điện thoại'
                                placeholder='Nhập số điện thoại cá nhân'
                                required={true}
                            />
                        </Col>
                        <Col md='12'>
                            <FormController
                                controlType='select'
                                name='departmentId'
                                data={formattedDepartments}
                                label='Phòng ban'
                                placeholder='Chọn phòng ban'
                                required={false}
                            />
                        </Col>

                        <Col md='12'>
                            <FormController
                                controlType='select'
                                name='positionId'
                                data={formattedPositions}
                                label='Chức vụ'
                                placeholder='Chọn chức vụ'
                                required={false}
                            />
                        </Col>
                        <Col md='12'>
                            <FormController
                                controlType='textInput'
                                name='roleName'
                                label='Vai trò'
                                placeholder='Nhập vai trò của cá nhân trong khách hàng'
                                required={false}
                            />
                        </Col>
                    </Row>
                </ModalBody>
                <ModalFooter>
                    <Button color='secondary' onClick={toggle}>
                        Hủy
                    </Button>
                    <Button color='primary' onClick={handleSubmit}>
                        Tạo mới
                    </Button>
                </ModalFooter>
            </FormProvider>
        </Modal>
    );
};

export default AddContactModal;
