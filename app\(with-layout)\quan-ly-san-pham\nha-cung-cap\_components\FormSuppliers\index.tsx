'use client';

import { useDeleteSupplier } from '@/apis/supplier/supplier.api';
import { ISupplier } from '@/apis/supplier/supplier.type';
import FormController from '@/components/common/FormController';
import { commonStatus } from '@/constants/sharedData/sharedData';
import { ROUTES } from '@/lib/routes';
import { showToastSuccess } from '@/utils/toast-message';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import React, { useRef, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { toast } from 'react-toastify';
import {
    Button,
    Card,
    Col,
    DropdownItem,
    DropdownMenu,
    DropdownToggle,
    Label,
    Row,
    UncontrolledDropdown,
} from 'reactstrap';

interface FormSuppliersProps {
    page: string;
    onSubmit?: (data: ISupplier) => void;
    onClose?: () => void;
    initValue?: ISupplier;
}
const FormSuppliers = ({
    page,
    onSubmit,
    onClose,
    initValue,
}: FormSuppliersProps) => {
    const router = useRouter();

    const formatDateTime = (dateTimeString: string): string => {
        if (!dateTimeString) {
            return '';
        }
        try {
            const date = new Date(dateTimeString);
            const day = date.getDate().toString().padStart(2, '0');
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const year = date.getFullYear();
            return `${day}/${month}/${year}`;
        } catch {
            return dateTimeString;
        }
    };

    const methods = useForm<ISupplier>({
        defaultValues: initValue
            ? {
                  ...initValue,
                  commonStatus:
                      (initValue.commonStatus?.toString() as unknown as number) ||
                      undefined,
                  createdDateTime: initValue.createdDateTime
                      ? formatDateTime(initValue.createdDateTime)
                      : '',
              }
            : {},
    });

    React.useEffect(() => {
        if (initValue) {
            methods.reset({
                ...initValue,

                commonStatus:
                    (initValue.commonStatus?.toString() as unknown as number) ||
                    undefined,
                createdDateTime: initValue.createdDateTime
                    ? formatDateTime(initValue.createdDateTime)
                    : '',
            });
        }
    }, [initValue, methods]);

    React.useEffect(() => {
        if (!initValue?.country) {
            methods.setValue('country' as keyof ISupplier, 'Việt Nam');
        }
    }, [methods, initValue]);

    const [image, setImage] = useState<string | null>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);

    const handleIconClick = () => {
        fileInputRef.current?.click();
    };

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = (ev) => {
                setImage(ev.target?.result as string);
            };
            reader.readAsDataURL(file);
        }
    };

    const handleUpdate = (id: string) => {
        router.push(
            ROUTES.PRODUCT_MANAGEMENT.SUPPLIERS.UPDATE.replace(':id', id),
        );
    };
    const handleFormSubmit = (data: ISupplier) => {
        if (onSubmit) {
            onSubmit(data);
        }
    };
    const { mutate: deleteSupplier } = useDeleteSupplier({
        onSuccess: () => {
            showToastSuccess({
                title: 'Xóa nhà cung cấp thành công',
                message:
                    'Thông tin nhà cung cấp đã được xóa thành công trong hệ thống.',
            });
            router.push(ROUTES.PRODUCT_MANAGEMENT.SUPPLIERS.INDEX);
        },
        onError: (error) => {
            const status = error.status;
            if (status === 400 || 401) {
                toast.warning(error.message);
                return;
            }
            toast.error(error.message);
        },
    });
    const handleDelete = () => {
        if (initValue?.id) {
            deleteSupplier({ ids: [initValue.id], isDeleted: false });
        }
    };
    return (
        <FormProvider {...methods}>
            <Card style={{ padding: '20px 40px 20px 40px' }}>
                {page === 'chi-tiet' && (
                    <Col md={12} style={{ padding: '10px 40px 0px 40px' }}>
                        <div className='d-flex justify-content-end gap-3 align-items-center pe-3'>
                            <Button
                                className='d-flex align-items-center gap-2 btn-outline-primary hover-primary'
                                style={{
                                    padding: '4px 8px',
                                    fontSize: '12px',
                                    transition: 'all 0.2s ease',
                                    backgroundColor: 'transparent',
                                    border: '1px solid #0ab39c',
                                    color: '#0ab39c',
                                    borderRadius: '6px',
                                    height: '32px',
                                    fontWeight: 500,
                                }}
                                onClick={() => handleUpdate(initValue?.id)}
                            >
                                <i className='ri-pencil-line'></i>
                                Chỉnh sửa
                            </Button>
                            <UncontrolledDropdown>
                                <DropdownToggle
                                    tag='button'
                                    className='btn'
                                    style={{
                                        backgroundColor: '#0ab39c',
                                        border: 'none',
                                        padding: '4px',
                                        minWidth: '30px',
                                    }}
                                >
                                    <i
                                        className='ri-more-fill'
                                        style={{
                                            color: 'white',
                                        }}
                                    ></i>
                                </DropdownToggle>
                                <DropdownMenu end>
                                    <DropdownItem>
                                        <i className='ri-history-line me-2'></i>
                                        Nhật ký nhà cung cấp
                                    </DropdownItem>
                                    <DropdownItem
                                        className='text-danger'
                                        onClick={handleDelete}
                                    >
                                        <i className='ri-delete-bin-line me-2'></i>
                                        Xóa
                                    </DropdownItem>
                                </DropdownMenu>
                            </UncontrolledDropdown>
                        </div>
                    </Col>
                )}

                <Row className='mt-4' style={{ padding: '0px 40px 20px 40px' }}>
                    {(page === 'chi-tiet' || page === 'chinh-sua') && (
                        <Col
                            md={6}
                            style={{ marginTop: '10px', marginBottom: '10px' }}
                        >
                            <FormController
                                controlType='textInput'
                                name='code'
                                label='Mã nhà cung cấp'
                                placeholder='Nhập nhà cung cấp'
                                required={true}
                                readOnly={page === 'chi-tiet'}
                            />
                        </Col>
                    )}
                    <Col
                        md={6}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <FormController
                            controlType='textInput'
                            name='name'
                            label='Tên nhà cung cấp'
                            placeholder='Nhập nhà cung cấp'
                            required={true}
                            readOnly={page === 'chi-tiet'}
                        />
                    </Col>

                    <Col
                        md={6}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <FormController
                            controlType='textInput'
                            name='tax'
                            label='Mã số thuế'
                            placeholder='Nhập mã số thuế'
                            required={false}
                            readOnly={page === 'chi-tiet'}
                        />
                    </Col>
                    <Col
                        md={12}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <FormController
                            name='description'
                            controlType='textarea'
                            label='Mô tả chung'
                            readOnly={page === 'chi-tiet'}
                            placeholder='Mô tả chung về nhà cung cấp'
                        />
                    </Col>
                    <Col
                        md={12}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <Label>Hình ảnh</Label>
                        <div
                            style={{
                                position: 'relative',
                                width: '300px',
                                height: '200px',
                                display: 'flex',
                                justifyContent: 'center',
                                cursor: 'pointer',
                            }}
                            onClick={handleIconClick}
                        >
                            {image ? (
                                <Image
                                    src={image}
                                    alt='Preview'
                                    fill
                                    style={{
                                        objectFit: 'cover',
                                        marginLeft: '30px',
                                    }}
                                />
                            ) : (
                                <i
                                    className='ri-image-add-fill'
                                    style={{ fontSize: '100px', color: '#ccc' }}
                                ></i>
                            )}
                            <input
                                type='file'
                                accept='image/*'
                                ref={fileInputRef}
                                style={{ display: 'none' }}
                                onChange={handleFileChange}
                            />
                        </div>
                    </Col>
                    <Col
                        md={6}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <FormController
                            controlType='textInput'
                            name='phoneNumber'
                            label='Số điện thoại'
                            placeholder='Nhập số điện thoại'
                            required={true}
                            readOnly={page === 'chi-tiet'}
                        />
                    </Col>

                    <Col
                        md={6}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <FormController
                            controlType='textInput'
                            name='email'
                            label='Email'
                            placeholder='Nhập email'
                            required={false}
                            readOnly={page === 'chi-tiet'}
                        />
                    </Col>

                    <Col
                        md={6}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <FormController
                            controlType='select'
                            name='country'
                            label='Quốc gia'
                            data={[
                                {
                                    label: 'Việt Nam',
                                    value: 'Việt Nam',
                                },
                            ]}
                            required={false}
                        />
                    </Col>

                    {(page === 'chi-tiet' || page === 'chinh-sua') && (
                        <Col
                            md={6}
                            style={{ marginTop: '10px', marginBottom: '10px' }}
                        >
                            <FormController
                                controlType='select'
                                name='commonStatus'
                                label='Tình trạng'
                                data={commonStatus}
                                required={true}
                                readOnly={page === 'chi-tiet'}
                            />
                        </Col>
                    )}

                    {page === 'chi-tiet' && (
                        <>
                            <Col
                                md={6}
                                style={{
                                    marginTop: '10px',
                                    marginBottom: '10px',
                                }}
                            >
                                <FormController
                                    controlType='textInput'
                                    name='userNameCreated'
                                    label='Người tạo'
                                    placeholder='Nguyễn Văn A'
                                    readOnly={page === 'chi-tiet'}
                                />
                            </Col>

                            <Col
                                md={6}
                                style={{
                                    marginTop: '10px',
                                    marginBottom: '10px',
                                }}
                            >
                                <FormController
                                    controlType='textInput'
                                    name='createdDateTime'
                                    label='Ngày tạo'
                                    readOnly={page === 'chi-tiet'}
                                />
                            </Col>
                        </>
                    )}
                </Row>

                {(page === 'tao-moi' || page === 'chinh-sua') && (
                    <div className='d-flex justify-content-end mt-4'>
                        <Button
                            color='danger'
                            className='me-2'
                            type='button'
                            onClick={onClose}
                        >
                            Hủy
                        </Button>
                        {page === 'tao-moi' && (
                            <Button
                                color='success'
                                type='button'
                                onClick={methods.handleSubmit(handleFormSubmit)}
                            >
                                Tạo mới
                            </Button>
                        )}
                        {page === 'chinh-sua' && (
                            <Button
                                color='success'
                                type='button'
                                onClick={methods.handleSubmit(handleFormSubmit)}
                            >
                                Lưu
                            </Button>
                        )}
                    </div>
                )}
            </Card>
        </FormProvider>
    );
};

export default FormSuppliers;
