import { <PERSON><PERSON>, <PERSON>, CardB<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from 'reactstrap';

import { ICustomerGroupsDetail } from '@/apis/customer-groups/customer-groups.type';
import BoxAddInfo from '@/components/common/BoxAddInfo';

interface FileInfoProps {
    data: ICustomerGroupsDetail;
}
const FileInfo = ({ data }: FileInfoProps) => {
    const { companies } = data;

    return (
        <Col lg={3}>
            <Card className='mb-3'>
                <CardHeader className='border-bottom-0'>
                    <div className='d-flex align-items-center'>
                        <h5 className='mb-0 flex-grow-1'>File đính kèm (2)</h5>
                        <Button
                            color='success'
                            size='sm'
                            className='btn-sm'
                            style={{
                                backgroundColor: '#0ab39c',
                                border: 'none',
                            }}
                        >
                            <i className='ri-add-line align-middle'></i> Thêm
                        </Button>
                    </div>
                </CardHeader>
                <CardBody>
                    {[
                        {
                            name: 'CRM.docx',
                            icon: 'ri-file-text-line',
                            date: '11/01/2025',
                        },
                        {
                            name: 'CRM.ppt',
                            icon: 'ri-file-ppt-line',
                            date: '11/01/2025',
                        },
                    ].map((file, index) => (
                        <div className='mb-3' key={index}>
                            <div className='d-flex align-items-center mb-2'>
                                <i
                                    className={`${file.icon} text-primary fs-18 me-2`}
                                ></i>
                                <div className='flex-grow-1'>
                                    <h6 className='mb-0'>{file.name}</h6>
                                    <small className='text-muted'>
                                        Ngày tải lên: {file.date}
                                    </small>
                                </div>
                                <div className='d-flex'>
                                    <Button
                                        color='light'
                                        size='sm'
                                        className='btn-icon me-1'
                                    >
                                        <i className='ri-download-line'></i>
                                    </Button>
                                    <Button
                                        color='light'
                                        size='sm'
                                        className='btn-icon'
                                    >
                                        <i className='ri-delete-bin-line'></i>
                                    </Button>
                                </div>
                            </div>
                        </div>
                    ))}

                    <Button color='info' outline block>
                        Xem thêm
                    </Button>
                </CardBody>
            </Card>

            <BoxAddInfo
                title='Khách hàng'
                length={companies.length}
                content={companies.map((item) => ({
                    title: item.name,
                    data: [
                        {
                            label: 'Loại hình',
                            value: item.businessTypeName,
                            boxColor: false,
                        },
                        {
                            label: 'Lĩnh vực',
                            value: item.industryName,
                            boxColor: false,
                        },
                        {
                            label: 'Giai đoạn',
                            value: item.lifecycleStageName,
                            boxColor: true,
                        },
                    ],
                    // onViewQuick: () => {},
                    // onViewDetail: () => {},
                    // onDelete: () => {},
                }))}
                // onAdd={() => {}}
            />
        </Col>
    );
};

export default FileInfo;
