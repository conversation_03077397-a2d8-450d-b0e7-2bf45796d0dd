'use client';

import { useGetQuotesTemplate } from '@/apis/quotes/quotes.api';
import { Option } from '@/types/app.type';
import {
    CheckIcon,
    Combobox,
    Group,
    Pill,
    PillsInput,
    useCombobox,
} from '@mantine/core';
import { useDebouncedValue } from '@mantine/hooks';
import { IconSelector } from '@tabler/icons-react';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { useFormContext } from 'react-hook-form';

interface Props {
    name: string;
    label?: string;
    placeholder?: string;
    style?: React.CSSProperties;
    searchInit?: string;
    readOnly?: boolean;
    description?: string;
    required?: boolean;
}

const ComboboxSelectQuoteTemplateControl = (props: Props) => {
    const {
        name,
        label,
        placeholder = 'Khách hàng',
        style = {},
        searchInit,
        readOnly,
        description,
        required,
    } = props;

    const stylePillsInput: React.CSSProperties = { width: '250px', ...style };

    const isSettedSearch = useRef<boolean>(false);

    const combobox = useCombobox({
        onDropdownClose: () => combobox.resetSelectedOption(),
        onDropdownOpen: () => combobox.updateSelectedOptionIndex('active'),
    });

    const [search, setSearch] = useState('');

    const methods = useFormContext();
    const { watch, setValue } = methods;
    const value = watch(name) ?? '';

    const [debouncedSearch] = useDebouncedValue(search, 1500);

    const { data: dataQuotesTemplate, isLoading } = useGetQuotesTemplate({
        Name: debouncedSearch,
    });

    const quotesTemplate = useMemo(() => {
        if (dataQuotesTemplate?.data?.items) {
            return dataQuotesTemplate.data.items.map((item) => ({
                value: item.id,
                label: item.name,
            }));
        }

        return [];
    }, [dataQuotesTemplate?.data?.items]);

    const handleValueSelect = (val: string) => {
        setValue(name, val);
        combobox.closeDropdown();
    };

    const valueSelected = useMemo(() => {
        const optionSelected = quotesTemplate.find(
            (quoteTemplate) => quoteTemplate.value === value,
        );

        if (!optionSelected) {
            return null;
        }

        return (
            <Pill
                key={value}
                withRemoveButton
                onRemove={() => setValue(name, '')}
            >
                {optionSelected.label}
            </Pill>
        );
    }, [name, quotesTemplate, setValue, value]);

    const options = quotesTemplate
        .filter((quoteTemplate: Option) =>
            quoteTemplate.label
                .toLowerCase()
                .includes(search.trim().toLowerCase()),
        )
        .map((quoteTemplate: Option) => (
            <Combobox.Option
                value={quoteTemplate.value}
                key={quoteTemplate.value}
                active={value === quoteTemplate.value}
            >
                <Group gap='sm'>
                    {value === quoteTemplate.value && <CheckIcon size={12} />}
                    <span>{quoteTemplate.label}</span>
                </Group>
            </Combobox.Option>
        ));

    useEffect(() => {
        if (searchInit && !isSettedSearch.current) {
            setSearch(searchInit);
            isSettedSearch.current = true;
        }
    }, [searchInit]);

    return (
        <Combobox
            store={combobox}
            onOptionSubmit={handleValueSelect}
            readOnly={readOnly}
        >
            <Combobox.DropdownTarget>
                <PillsInput
                    style={stylePillsInput}
                    onClick={() => {
                        if (!readOnly) {
                            combobox.openDropdown();
                        }
                    }}
                    label={label}
                    description={description}
                    required={required}
                    rightSection={<IconSelector size={17} stroke={1.5} />}
                >
                    <Pill.Group>
                        {valueSelected ? (
                            valueSelected
                        ) : (
                            <Combobox.EventsTarget>
                                <PillsInput.Field
                                    value={search}
                                    placeholder={!value ? placeholder : ''}
                                    readOnly={readOnly}
                                    onChange={(event) => {
                                        combobox.updateSelectedOptionIndex();
                                        setSearch(event.currentTarget.value);
                                    }}
                                    onKeyDown={() => {
                                        setValue(name, '');
                                    }}
                                    onFocus={() => {
                                        if (!readOnly) {
                                            combobox.openDropdown();
                                        }
                                    }}
                                    onBlur={() => {
                                        combobox.closeDropdown();
                                        setSearch(value || '');
                                    }}
                                />
                            </Combobox.EventsTarget>
                        )}
                    </Pill.Group>
                </PillsInput>
            </Combobox.DropdownTarget>

            <Combobox.Dropdown>
                <Combobox.Options mah={200} style={{ overflowY: 'auto' }}>
                    {options.length > 0 ? (
                        options
                    ) : (
                        <Combobox.Empty>
                            {isLoading
                                ? 'Đang tìm kiếm mẫu báo giá...'
                                : 'Không tìm thấy mẫu báo giá...'}
                        </Combobox.Empty>
                    )}
                </Combobox.Options>
            </Combobox.Dropdown>
        </Combobox>
    );
};

export default ComboboxSelectQuoteTemplateControl;
