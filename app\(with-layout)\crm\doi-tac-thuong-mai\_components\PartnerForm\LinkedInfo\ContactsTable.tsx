import { useFieldArray, useFormContext } from 'react-hook-form';
import {
    Button,
    Dropdown,
    DropdownItem,
    DropdownMenu,
    DropdownToggle,
} from 'reactstrap';
import { useState } from 'react';
import { IContactResponse } from '@/apis/contact/contact.type';
import { IPartnerPayload } from '@/apis/partners/partners.type';

interface ContactsTableProps {
    contactList: IContactResponse[];
    toggleAddContactModal: () => void;
    title: string;
    buttonText: string;
}

const ContactsTable = ({
    contactList,
    toggleAddContactModal,
    title,
    buttonText,
}: ContactsTableProps) => {
    const [contactDropdownOpen, setContactDropdownOpen] = useState<
        number | null
    >(null);

    const { control } = useFormContext<IPartnerPayload>();
    const { fields, append, remove, update } = useFieldArray({
        control,
        name: 'contacts',
        keyName: 'fieldId',
    });

    const toggleContactDropdown = (index: number) => {
        setContactDropdownOpen(contactDropdownOpen === index ? null : index);
    };

    const handleAddContact = () => {
        append({
            id: '',
        });
    };

    const handleSelectContact = (index: number, contact: IContactResponse) => {
        update(index, {
            ...fields[index],
            id: contact.id,
        });
        setContactDropdownOpen(null);
    };

    return (
        <div>
            <div className='d-flex justify-content-between align-items-center'>
                <h6 className='mb-0'>{title}</h6>
                <Button
                    size='sm'
                    className='d-flex align-items-center'
                    onClick={handleAddContact}
                    style={{
                        border: '1px solid #60cfbf',
                        backgroundColor: '#ffffff',
                        color: '#60cfbf',
                    }}
                >
                    <i className='ri-add-line me-1'></i> {buttonText}
                </Button>
            </div>
            <table
                className='table table-bordered mb-0'
                style={{ tableLayout: 'fixed', width: '100%' }}
            >
                <thead>
                    <tr>
                        <th style={{ width: '100px' }}>Dòng</th>
                        <th>Họ và tên</th>
                        <th>Phòng ban</th>
                        <th>Chức vụ</th>
                        <th>Vai trò</th>
                        <th style={{ width: '100px' }}></th>
                    </tr>
                </thead>
                <tbody>
                    {fields.map((field, index) => {
                        const contact = contactList.find(
                            (c) => c.id === field.id,
                        );
                        return (
                            <tr key={field.fieldId}>
                                <td>{index + 1}</td>
                                <td>
                                    <Dropdown
                                        isOpen={contactDropdownOpen === index}
                                        toggle={() =>
                                            toggleContactDropdown(index)
                                        }
                                    >
                                        <DropdownToggle
                                            tag='div'
                                            style={{ cursor: 'pointer' }}
                                        >
                                            {contact?.name || 'Chọn cá nhân'}
                                        </DropdownToggle>
                                        <DropdownMenu
                                            style={{
                                                width: '300px',
                                                maxHeight: '200px',
                                                overflowY: 'auto',
                                            }}
                                        >
                                            {contactList
                                                .filter(
                                                    (option) =>
                                                        !fields.some(
                                                            (field) =>
                                                                field.id ===
                                                                option.id,
                                                        ),
                                                )
                                                .map((option, optIndex) => (
                                                    <DropdownItem
                                                        key={optIndex}
                                                        onClick={() =>
                                                            handleSelectContact(
                                                                index,
                                                                option,
                                                            )
                                                        }
                                                    >
                                                        <div>
                                                            <strong>
                                                                {option.name}
                                                            </strong>
                                                        </div>
                                                        <div className='text-muted small'>
                                                            {
                                                                option.departmentName
                                                            }{' '}
                                                            -{' '}
                                                            {
                                                                option.positionName
                                                            }{' '}
                                                            -{' '}
                                                            <span className='text-info'>
                                                                {
                                                                    option.roleName
                                                                }
                                                            </span>
                                                        </div>
                                                    </DropdownItem>
                                                ))}
                                            <DropdownItem divider />
                                            <DropdownItem
                                                onClick={toggleAddContactModal}
                                            >
                                                <div className='text-primary'>
                                                    <i className='ri-add-line me-1'></i>
                                                    Thêm cá nhân
                                                </div>
                                            </DropdownItem>
                                        </DropdownMenu>
                                    </Dropdown>
                                </td>
                                <td>{contact?.departmentName || ''}</td>
                                <td>{contact?.positionName || ''}</td>
                                <td>{contact?.roleName || ''}</td>
                                <td>
                                    <button
                                        type='button'
                                        className='btn btn-sm btn-danger'
                                        onClick={() => remove(index)}
                                    >
                                        <i className='ri-delete-bin-line'></i>
                                    </button>
                                </td>
                            </tr>
                        );
                    })}
                    {fields.length === 0 && (
                        <tr>
                            <td colSpan={6} className='text-center py-3'>
                                Chưa có dữ liệu. Vui lòng thêm cá nhân.
                            </td>
                        </tr>
                    )}
                </tbody>
            </table>
        </div>
    );
};

export default ContactsTable;
