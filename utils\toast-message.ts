import { notifications } from '@mantine/notifications';

interface ToastParams {
    title: string;
    message?: string;
}

export const showToastSuccess = (toast: ToastParams) => {
    const { title, message } = toast;

    notifications.show({
        title: title || 'Thành công',
        message,
        color: 'green',
        autoClose: 3000,
    });
};

export const showToastError = (toast: ToastParams) => {
    const { title, message } = toast;

    notifications.show({
        title: title || 'Lỗi',
        message,
        color: 'red',
        autoClose: 3000,
    });
};

export const showToastInformation = (toast: ToastParams) => {
    const { title, message } = toast;

    notifications.show({
        title: title || 'Thông tin',
        message,
        color: 'blue',
        autoClose: 3000,
    });
};

export const showToastWarning = (toast: ToastParams) => {
    const { title, message } = toast;

    notifications.show({
        title: title || 'Cảnh báo',
        message,
        color: 'yellow',
        autoClose: 3000,
    });
};
