'use client';

import { useGetQuoteTemplateDetail } from '@/apis/quotes/quote-templates.api';
import { IQuoteTemplateDetail } from '@/apis/quotes/quote-templates.type';
import { useParams } from 'next/navigation';
import { useMemo } from 'react';
import QuotationForm from '../../../_components/QuotationForm';

const DetailQuotationForm = () => {
    const params = useParams();
    const id = params.id as string;

    const { data } = useGetQuoteTemplateDetail({ id }, { enabled: !!id });

    const quoteTemplateDetail = useMemo(() => {
        if (data?.data) {
            return {
                ...data.data,
                templateType: data.data.templateType.toString(),
            };
        }

        return {};
    }, [data?.data]);

    return (
        <QuotationForm
            initValue={quoteTemplateDetail as IQuoteTemplateDetail}
            mode='detail'
        />
    );
};

export default DetailQuotationForm;
