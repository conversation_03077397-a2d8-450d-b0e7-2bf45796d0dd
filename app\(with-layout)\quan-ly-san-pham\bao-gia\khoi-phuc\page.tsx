'use client';
import {
    useGetSearchRestoreQuotes,
    useRestoreQuotes,
} from '@/apis/quotes/quotes.api';
import { IQuoteParams, IQuoteReponse } from '@/apis/quotes/quotes.type';
import InputDateRangePickerControl from '@/components/common/FormController/InputDateRangePickerControl';
import InputSearchNameWithApiControl from '@/components/common/FormController/InputSearchNameWithApiControl';
import { ROUTES } from '@/lib/routes';
import { getOneMonthAgo, getToday } from '@/utils/time';
import { showToastSuccess } from '@/utils/toast-message';
import dynamic from 'next/dynamic';
import { useRouter } from 'next/navigation';
import { useMemo, useState } from 'react';
import { FormProvider, useForm, useWatch } from 'react-hook-form';
import { toast } from 'react-toastify';
import { <PERSON><PERSON>, Card, CardHeader, Col, Container } from 'reactstrap';
import useGetColumn from '../_hook/useGetColumn';

const MantineTable = dynamic(
    () =>
        import('@/components/common/MantineReactTable').then(
            (mod) => mod.default as typeof mod.default<IQuoteReponse>,
        ),
    {
        ssr: false,
    },
) as typeof import('@/components/common/MantineReactTable').default;

const RestoreQuotes = () => {
    const router = useRouter();
    const [selectedIds, setSelectedIds] = useState<string[]>([]);
    const methods = useForm<IQuoteParams>({
        defaultValues: {
            PageNumber: 1,
            PageSize: 10,
            SortField: '',
            IsDescending: false,
            FromDate: getOneMonthAgo(),
            ToDate: getToday(),
        },
    });

    const { control, setValue } = methods;

    const [
        FieldSearch,
        PageNumber,
        PageSize,
        SortField,
        IsDescending,
        Status,
        FromDate,
        ToDate,
    ] = useWatch({
        control,
        name: [
            'FieldSearch',
            'PageNumber',
            'PageSize',
            'SortField',
            'IsDescending',
            'Status',
            'FromDate',
            'ToDate',
        ],
    });

    const { data, isLoading: isLoadingQuotes } = useGetSearchRestoreQuotes({
        FieldSearch,
        PageNumber,
        PageSize,
        SortField,
        IsDescending,
        Status,
        FromDate,
        ToDate,
    });

    const { dataRestoreQuotes, totalItems } = useMemo(() => {
        const dataRestoreQuotes = data?.data?.items ?? [];
        const totalItems = data?.data?.totalItems ?? 0;

        return { totalItems, dataRestoreQuotes };
    }, [data]);

    const { mutate: restoreQuotes } = useRestoreQuotes({
        onSuccess: () => {
            showToastSuccess({
                title: 'Khôi phục báo giá thành công',
                message:
                    'Thông tin báo giá đã được khôi phục thành công trong hệ thống.',
            });
            setSelectedIds([]);
        },
        onError: () => {
            toast.error('Khôi phục báo giá thất bại');
        },
    });

    const handleRestoreQuote = (quote?: IQuoteReponse) => {
        if (quote) {
            restoreQuotes([quote?.id ?? '']);
        }
    };

    const handleRestoreSelected = () => {
        if (selectedIds.length > 0) {
            restoreQuotes(selectedIds);
        } else {
            toast.warning('Vui lòng chọn ít nhất một báo giá để khôi phục');
        }
    };

    const columns = useGetColumn({
        page: 'restore',
        onRestore: handleRestoreQuote,
    });

    const handlePageChange = (page: number) => {
        setValue('PageNumber', page);
    };

    const handlePageSizeChange = (size: number) => {
        setValue('PageSize', size);
    };

    return (
        <FormProvider {...methods}>
            <Container fluid>
                <Col xl={12}>
                    <Card>
                        <CardHeader>
                            <div className='d-flex justify-content-between align-items-center'>
                                <div className='d-flex gap-3 justify-content-between'>
                                    <div className='d-flex flex-column'>
                                        <div className='d-flex align-items-center gap-3'>
                                            <div className='me-2'>
                                                <InputDateRangePickerControl
                                                    nameFrom='FromDate'
                                                    nameTo='ToDate'
                                                    allowFutureDates={false}
                                                />
                                            </div>
                                        </div>
                                    </div>
                                    <InputSearchNameWithApiControl
                                        name='Name'
                                        placeholder='Tìm kiếm tên báo giá'
                                    />
                                </div>
                                <div className='d-flex justify-content-end gap-2'>
                                    <Button
                                        color='success'
                                        onClick={handleRestoreSelected}
                                        disabled={selectedIds.length === 0}
                                    >
                                        {selectedIds.length > 0
                                            ? `Khôi phục (${selectedIds.length})`
                                            : 'Khôi phục'}
                                    </Button>
                                    <Button
                                        style={{
                                            backgroundColor: 'white',
                                            color: '#0ab39c',
                                            borderColor: '#0ab39c',
                                        }}
                                        onClick={() => {
                                            router.push(
                                                ROUTES.PRODUCT_MANAGEMENT.QUOTES
                                                    .INDEX,
                                            );
                                        }}
                                    >
                                        <i className=' ri-arrow-go-back-line'></i>
                                    </Button>
                                </div>
                            </div>
                        </CardHeader>
                        <MantineTable
                            columns={columns}
                            data={dataRestoreQuotes}
                            isLoading={isLoadingQuotes}
                            totalItems={totalItems}
                            onPageChange={handlePageChange}
                            onPageSizeChange={handlePageSizeChange}
                            tableProps={{
                                enableRowSelection: true,
                                enableMultiRowSelection: true,
                                selectAllMode: 'page',
                                initialState: {
                                    pagination: {
                                        pageIndex: (PageNumber || 1) - 1,
                                        pageSize: PageSize || 10,
                                    },
                                },
                                state: {
                                    pagination: {
                                        pageIndex: (PageNumber || 1) - 1,
                                        pageSize: PageSize || 10,
                                    },
                                    rowSelection: selectedIds.reduce(
                                        (acc, id) => {
                                            const index =
                                                dataRestoreQuotes.findIndex(
                                                    (quote) => quote.id === id,
                                                );
                                            if (index !== -1) {
                                                acc[index] = true;
                                            }
                                            return acc;
                                        },
                                        {} as Record<string, boolean>,
                                    ),
                                },
                                onRowSelectionChange: (updater) => {
                                    let selectedRows: Record<string, boolean>;
                                    if (typeof updater === 'function') {
                                        const currentSelection =
                                            selectedIds.reduce(
                                                (acc, id) => {
                                                    const index =
                                                        dataRestoreQuotes.findIndex(
                                                            (quote) =>
                                                                quote.id === id,
                                                        );
                                                    if (index !== -1) {
                                                        acc[index] = true;
                                                    }
                                                    return acc;
                                                },
                                                {} as Record<string, boolean>,
                                            );
                                        selectedRows =
                                            updater(currentSelection);
                                    } else {
                                        selectedRows = updater;
                                    }

                                    const newSelectedIds = Object.keys(
                                        selectedRows,
                                    )
                                        .filter((key) => selectedRows[key])
                                        .map(
                                            (key) =>
                                                dataRestoreQuotes[parseInt(key)]
                                                    ?.id,
                                        )
                                        .filter(
                                            (id): id is string =>
                                                id !== undefined,
                                        );

                                    setSelectedIds(newSelectedIds);
                                },
                                mantineTableBodyRowProps: ({ row }) => ({
                                    onClick: (e) => {
                                        if (
                                            (e.target as HTMLElement).closest(
                                                '.mantine-Checkbox-root',
                                            ) ||
                                            (e.target as HTMLElement)
                                                .tagName === 'INPUT'
                                        ) {
                                            e.stopPropagation();
                                            return;
                                        }
                                        if (row.original.id) {
                                            router.push(
                                                ROUTES.PRODUCT_MANAGEMENT.QUOTES.DETAIL.replace(
                                                    ':id',
                                                    row.original.id,
                                                ),
                                            );
                                        }
                                    },
                                    sx: {
                                        cursor: 'pointer',
                                    },
                                }),
                                onPaginationChange: (updater) => {
                                    let newPagination;
                                    if (typeof updater === 'function') {
                                        newPagination = updater({
                                            pageIndex: (PageNumber || 1) - 1,
                                            pageSize: PageSize || 10,
                                        });
                                    } else {
                                        newPagination = updater;
                                    }

                                    if (
                                        newPagination.pageIndex !==
                                        (PageNumber || 1) - 1
                                    ) {
                                        handlePageChange(
                                            newPagination.pageIndex + 1,
                                        );
                                    }

                                    if (
                                        newPagination.pageSize !==
                                        (PageSize || 10)
                                    ) {
                                        handlePageSizeChange(
                                            newPagination.pageSize,
                                        );
                                    }
                                },
                            }}
                        />
                    </Card>
                </Col>
            </Container>
        </FormProvider>
    );
};
export default RestoreQuotes;
