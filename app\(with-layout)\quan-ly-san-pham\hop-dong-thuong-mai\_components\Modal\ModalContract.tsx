import { ROUTES } from '@/lib/routes';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>Header, <PERSON>dal<PERSON><PERSON>, <PERSON>dalFooter, Button } from 'reactstrap';
import { Radio, Stack } from '@mantine/core';

interface ContractModalProps {
    isOpen: boolean;
    toggle: () => void;
}
const ContractModal = ({ isOpen, toggle }: ContractModalProps) => {
    const router = useRouter();
    const [selectedType, setSelectedType] = useState('asic');
    const handleRouter = () => {
        if (selectedType === 'asic') {
            router.push(ROUTES.PRODUCT_MANAGEMENT.CONTRACTS.CREATE_ASIC);
            return;
        }
        if (selectedType === 'customer') {
            router.push(ROUTES.PRODUCT_MANAGEMENT.CONTRACTS.CREATE_CUSTOMER);
            return;
        }
        return;
    };
    return (
        <Modal isOpen={isOpen} toggle={toggle} centered>
            <ModalHeader
                toggle={toggle}
                style={{ borderBottom: '1px solid #e9ebec', padding: '20px' }}
            >
                <PERSON><PERSON><PERSON> hợp đồng
            </ModalHeader>
            <ModalBody style={{ padding: '20px' }}>
                <Stack gap='md'>
                    <Radio
                        checked={selectedType === 'asic'}
                        variant='outline'
                        onChange={() => setSelectedType('asic')}
                        label='Hợp đồng theo mẫu của ASIC'
                        color='#0ab39c'
                    />

                    <Radio
                        checked={selectedType === 'customer'}
                        variant='outline'
                        onChange={() => setSelectedType('customer')}
                        label='Hợp đồng theo mẫu khách hàng'
                        color='#0ab39c'
                    />
                </Stack>
            </ModalBody>
            <ModalFooter>
                <Button outline color='danger' onClick={toggle}>
                    Hủy
                </Button>
                <Button color='success' onClick={handleRouter}>
                    Tiếp tục
                </Button>
            </ModalFooter>
        </Modal>
    );
};
export default ContractModal;
