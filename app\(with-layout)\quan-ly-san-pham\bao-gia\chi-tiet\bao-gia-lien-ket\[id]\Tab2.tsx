import { useGetDetailCustomer } from '@/apis/customer/customer.api';
import { IQuoteDetailResponse } from '@/apis/quotes/quotes.type';
import FormatTextDetail from '@/components/common/FormatTextDetail';
import { Card, CardBody, Row, Col, Label } from 'reactstrap';
import { useState } from 'react';

interface Tab2Props {
    data?: IQuoteDetailResponse;
}

const Tab2 = ({ data }: Tab2Props) => {
    const CustomerId = data?.company.id;
    const { data: DataCustomer } = useGetDetailCustomer(CustomerId);
    const [showAllEmails, setShowAllEmails] = useState(false);
    const [showAllPhones, setShowAllPhones] = useState(false);
    if (!DataCustomer) {
        return <div className='p-4'>Đang tải thông tin khách hàng...</div>;
    }

    const phoneInfos =
        DataCustomer.data?.associatedInfos?.filter(
            (info) => info.associatedInfoType === 1,
        ) || [];
    const emailInfos =
        DataCustomer.data?.associatedInfos?.filter(
            (info) => info.associatedInfoType === 2,
        ) || [];
    const contactAddress = DataCustomer.data?.addresses?.find(
        (addr) => addr.addressType === 1,
    );
    const deliveryAddress = DataCustomer.data?.addresses?.find(
        (addr) => addr.addressType === 2,
    );
    const invoiceAddress = DataCustomer.data?.addresses?.find(
        (addr) => addr.addressType === 3,
    );

    const EmailDisplay = () => {
        if (emailInfos.length === 0) {
            return <span>Chưa có</span>;
        }

        if (emailInfos.length === 1) {
            return <span>{emailInfos[0].value}</span>;
        }

        return (
            <div>
                <div className='d-flex align-items-center gap-3'>
                    <span>{emailInfos[0].value}</span>
                    <div
                        className='ms-2 px-2 py-1 rounded d-inline-flex align-items-center'
                        style={{
                            backgroundColor: '#daf4f0',
                            color: '#0ab39c',
                            fontSize: '12px',
                            marginLeft: '20px',
                        }}
                    >
                        <span>+{emailInfos.length - 1}</span>
                    </div>
                    <div onClick={() => setShowAllEmails(!showAllEmails)}>
                        <i
                            className={`ri-arrow-${showAllEmails ? 'up' : 'down'}-s-line me-1`}
                            style={{ cursor: 'pointer', fontSize: '20px' }}
                        ></i>
                    </div>
                </div>
                {showAllEmails && (
                    <div className='mt-2'>
                        {emailInfos.slice(1).map((email) => (
                            <div key={email.id} className='mb-1'>
                                <span>{email.value}</span>
                            </div>
                        ))}
                    </div>
                )}
            </div>
        );
    };

    const PhoneDisplay = () => {
        if (phoneInfos.length === 0) {
            return <span>Chưa có</span>;
        }

        if (phoneInfos.length === 1) {
            return <span>{phoneInfos[0].value}</span>;
        }

        return (
            <div>
                <div className='d-flex align-items-center gap-3'>
                    <span>{phoneInfos[0].value}</span>
                    <div
                        className='ms-2 px-2 py-1 rounded d-inline-flex align-items-center'
                        style={{
                            backgroundColor: '#daf4f0',
                            color: '#0ab39c',
                            fontSize: '12px',
                            marginLeft: '20px',
                        }}
                    >
                        <span>+{phoneInfos.length - 1}</span>
                    </div>
                    <div onClick={() => setShowAllPhones(!showAllPhones)}>
                        <i
                            className={`ri-arrow-${showAllPhones ? 'up' : 'down'}-s-line me-1`}
                            style={{ cursor: 'pointer', fontSize: '20px' }}
                        ></i>
                    </div>
                </div>
                {showAllPhones && (
                    <div className='mt-2'>
                        {phoneInfos.slice(1).map((phone) => (
                            <div key={phone.id} className='mb-1'>
                                <span>{phone.value}</span>
                            </div>
                        ))}
                    </div>
                )}
            </div>
        );
    };

    const getInitials = (name: string) => {
        return name
            .split(' ')
            .map((word) => word.charAt(0))
            .join('')
            .toUpperCase()
            .slice(0, 2);
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('vi-VN');
    };

    const getStatusColor = (status: number) => {
        switch (status) {
            case 1:
                return 'bg-warning-subtle text-warning';
            case 2:
                return 'bg-success-subtle text-success';
            case 3:
                return 'bg-danger-subtle text-danger';
            default:
                return 'bg-secondary-subtle text-secondary';
        }
    };

    const getLifecycleColor = (stage: number) => {
        switch (stage) {
            case 1:
                return 'bg-primary-subtle text-primary';
            case 2:
                return 'bg-info-subtle text-info';
            case 3:
                return 'bg-success-subtle text-success';
            default:
                return 'bg-secondary-subtle text-secondary';
        }
    };

    return (
        <div className='p-2'>
            <div className='d-flex align-items-center'>
                <div
                    className='d-flex align-items-center justify-content-center fw-bold me-3'
                    style={{
                        width: '48px',
                        height: '48px',
                        backgroundColor: '#daf4f0',
                        color: '#0ab39c',
                        fontSize: '18px',
                        borderRadius: '50% !important',
                    }}
                >
                    {getInitials(DataCustomer.data?.name || '')}
                </div>
                <div>
                    <h2 className='mb-1 fw-bold'>{DataCustomer.data?.name}</h2>
                </div>
            </div>

            <Row className='mb-4'>
                <Col md={4} className='mb-3 mt-3'>
                    <Col md={12} className='mb-3 mt-3'>
                        <FormatTextDetail
                            label='MÃ KHÁCH HÀNG'
                            p={DataCustomer.data?.code || '-'}
                        />
                    </Col>
                    <Col md={12} className='mb-3 mt-3'>
                        <Label className='text-muted text-uppercase fw-medium'>
                            GIAI ĐOẠN
                        </Label>
                        <div>
                            <span
                                className={`badge ${getLifecycleColor(DataCustomer.data?.lifecycleStageEnum || 0)}`}
                            >
                                {DataCustomer.data?.lifecycleStageEnumName}
                            </span>
                        </div>
                    </Col>
                    <Col md={12} className='mb-3 mt-3'>
                        <FormatTextDetail
                            label='NGÀY TẠO'
                            p={
                                formatDate(
                                    DataCustomer.data?.createdDate || '',
                                ) || '-'
                            }
                        />
                    </Col>
                </Col>

                <Col md={4} className='mb-3 mt-3'>
                    <Col md={12} className='mb-3 mt-3'>
                        <Label className='text-muted text-uppercase fw-medium'>
                            EMAIL
                        </Label>
                        <p className='mb-0'>
                            <EmailDisplay />
                        </p>
                    </Col>
                    <Col md={12} className='mb-3 mt-3'>
                        <Label className='text-muted text-uppercase fw-medium'>
                            TRẠNG THÁI
                        </Label>
                        <div>
                            <span
                                className={`badge ${getStatusColor(DataCustomer.data?.leadStatus || 0)}`}
                            >
                                {DataCustomer.data?.leadStatusName}
                            </span>
                        </div>
                    </Col>
                </Col>

                <Col md={4} className='mb-3 mt-3'>
                    <Col md={12} className='mb-3 mt-3'>
                        <Label className='text-muted text-uppercase fw-medium'>
                            SỐ ĐIỆN THOẠI
                        </Label>
                        <p className='mb-0'>
                            <PhoneDisplay />
                        </p>
                    </Col>
                    <Col md={12} className='mb-3 mt-3'>
                        <FormatTextDetail
                            label='LĨNH VỰC'
                            p={DataCustomer.data?.industryName || '-'}
                        />
                    </Col>
                </Col>
            </Row>

            <Row>
                {contactAddress && (
                    <Col lg={4}>
                        <Card>
                            <CardBody style={{ border: '1px solid #ccc' }}>
                                <div className='d-flex align-items-center mb-3'>
                                    <i className='ri-map-pin-line me-2 text-muted'></i>
                                    <h6 className='mb-0 fw-medium'>
                                        ĐỊA CHỈ LIÊN HỆ
                                    </h6>
                                </div>
                                <div>
                                    <p className=' mb-2'>
                                        {contactAddress.addressName}
                                    </p>
                                    <p className=' text-muted mb-2'>
                                        {contactAddress.wardName},{' '}
                                        {contactAddress.districtName},{' '}
                                        {contactAddress.provinceName}
                                    </p>
                                    <div className='d-flex align-items-center text-muted'>
                                        <i className='ri-phone-line me-1'></i>
                                        <span>
                                            {phoneInfos.length > 0
                                                ? phoneInfos[0].value
                                                : 'Chưa có'}
                                        </span>
                                    </div>
                                </div>
                            </CardBody>
                        </Card>
                    </Col>
                )}

                {deliveryAddress && (
                    <Col lg={4} className='mb-3'>
                        <Card>
                            <CardBody style={{ border: '1px solid #ccc' }}>
                                <div className='d-flex align-items-center mb-3'>
                                    <i className='ri-building-line me-2 text-muted'></i>
                                    <h6 className='mb-0 fw-medium'>
                                        ĐỊA CHỈ GIAO HÀNG
                                    </h6>
                                </div>
                                <div>
                                    <p className=' mb-2'>
                                        {deliveryAddress.addressName}
                                    </p>
                                    <p className=' text-muted mb-2'>
                                        {deliveryAddress.wardName},{' '}
                                        {deliveryAddress.districtName},{' '}
                                        {deliveryAddress.provinceName}
                                    </p>
                                    <div className='d-flex align-items-center text-muted'>
                                        <i className='ri-phone-line me-1'></i>
                                        <span>
                                            {phoneInfos.length > 0
                                                ? phoneInfos[0].value
                                                : 'Chưa có'}
                                        </span>
                                    </div>
                                </div>
                            </CardBody>
                        </Card>
                    </Col>
                )}

                {invoiceAddress && (
                    <Col lg={4} className='mb-3'>
                        <Card>
                            <CardBody style={{ border: '1px solid #ccc' }}>
                                <div className='d-flex align-items-center mb-3'>
                                    <i className='ri-mail-line me-2 text-muted'></i>
                                    <h6 className='mb-0 fw-medium'>
                                        ĐỊA CHỈ XUẤT HÓA ĐƠN
                                    </h6>
                                </div>
                                <div>
                                    <p className=' mb-2'>
                                        {invoiceAddress.addressName}
                                    </p>
                                    <p className=' text-muted mb-2'>
                                        {invoiceAddress.wardName},{' '}
                                        {invoiceAddress.districtName},{' '}
                                        {invoiceAddress.provinceName}
                                    </p>
                                    <div className='d-flex align-items-center text-muted'>
                                        <i className='ri-phone-line me-1'></i>
                                        <span>
                                            {phoneInfos.length > 0
                                                ? phoneInfos[0].value
                                                : 'Chưa có'}
                                        </span>
                                    </div>
                                </div>
                            </CardBody>
                        </Card>
                    </Col>
                )}
            </Row>
        </div>
    );
};

export default Tab2;
