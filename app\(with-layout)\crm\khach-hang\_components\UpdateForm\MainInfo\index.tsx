'use client';
import { ICustomer } from '@/apis/customer/customer.type';
import Avatar from '@/components/common/Avatar';
import CollapseApp from '@/components/common/CollapseApp';
import FormController from '@/components/common/FormController';
import SelectBusinessTypeControl from '@/components/common/FormController/SelectBusinessTypeControl';
import SelectIndustryControl from '@/components/common/FormController/SelectIndustryControl';
import { LeadStatus, LifecycleStage } from '@/constants/sharedData/sharedData';
import { AssociatedInfoType } from '@/constants/sharedData/sharedData.enums';
import { IconPlus, IconX } from '@tabler/icons-react';

import { useFieldArray, useFormContext } from 'react-hook-form';
import { Col, FormGroup, Label, Row } from 'reactstrap';

const MainInfo = () => {
    const methods = useFormContext<ICustomer>();
    const { control, watch } = methods;

    const {
        fields: associatedInfoFields,
        append: appendAssociatedInfo,
        remove: removeAssociatedInfo,
    } = useFieldArray({
        control,
        name: 'associatedInfoDtos',
    });

    const emailFields =
        associatedInfoFields.filter(
            (field) => field.associatedInfoType === AssociatedInfoType.Email,
        ) || [];

    const phoneFields =
        associatedInfoFields.filter(
            (field) =>
                field.associatedInfoType === AssociatedInfoType.PhoneNumber,
        ) || [];

    const businessTypeId = watch('businessTypeId');

    const renderEmailFields = () => (
        <Col md='12'>
            <Label className='mb-2'>
                <strong style={{ fontWeight: 500 }}>
                    Email <span style={{ color: 'red' }}>*</span>
                </strong>
            </Label>
            {emailFields.length > 0 ? (
                emailFields.map((field, index) => {
                    const fieldIndex = associatedInfoFields.findIndex(
                        (item) => item.id === field.id,
                    );
                    return (
                        <div
                            key={`email-${field.id}`}
                            style={{ position: 'relative' }}
                            className='d-flex gap-3 mb-3'
                        >
                            <FormController
                                controlType='textInput'
                                name={`associatedInfoDtos.${fieldIndex}.value`}
                                label=''
                                placeholder='Nhập địa chỉ email của khách hàng'
                                className='w-100'
                                required={index === 0}
                            />
                            <div
                                style={{
                                    position: 'absolute',
                                    right: '-38px',
                                    top: '5px',
                                    borderRadius: '50%',
                                    border:
                                        index === emailFields.length - 1
                                            ? '1px solid #0ab39c'
                                            : '1px solid #dc3545',
                                    backgroundColor: '#ffffff',
                                    color:
                                        index === emailFields.length - 1
                                            ? '#0ab39c'
                                            : '#dc3545',
                                    display: 'flex',
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    width: '25px',
                                    height: '25px',
                                    cursor: 'pointer',
                                }}
                            >
                                {index === emailFields.length - 1 ? (
                                    <IconPlus
                                        size={18}
                                        stroke={1.5}
                                        onClick={() =>
                                            appendAssociatedInfo({
                                                value: '',
                                                associatedInfoType:
                                                    AssociatedInfoType.Email,
                                            })
                                        }
                                    />
                                ) : (
                                    <IconX
                                        size={18}
                                        stroke={1.5}
                                        onClick={() =>
                                            removeAssociatedInfo(fieldIndex)
                                        }
                                    />
                                )}
                            </div>
                        </div>
                    );
                })
            ) : (
                <div style={{ position: 'relative' }} className='d-flex gap-3'>
                    <FormController
                        controlType='textInput'
                        name='email-placeholder'
                        label=''
                        placeholder='Nhập địa chỉ email của khách hàng'
                        className='w-100'
                    />
                    <div
                        style={{
                            position: 'absolute',
                            right: '-24px',
                            top: '8px',
                            borderRadius: '50%',
                            backgroundColor: '#daf4f0',
                            color: '#0ab39c',
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                        }}
                    >
                        <IconPlus
                            size={18}
                            stroke={1.5}
                            onClick={() =>
                                appendAssociatedInfo({
                                    value: '',
                                    associatedInfoType:
                                        AssociatedInfoType.Email,
                                })
                            }
                        />
                    </div>
                </div>
            )}
        </Col>
    );

    const renderPhoneFields = () => (
        <Col md='12'>
            <Label className='mb-2'>
                <strong style={{ fontWeight: 500 }}>
                    Số điện thoại <span style={{ color: 'red' }}>*</span>
                </strong>
            </Label>
            {phoneFields.length > 0 ? (
                phoneFields.map((field, index) => {
                    const fieldIndex = associatedInfoFields.findIndex(
                        (item) => item.id === field.id,
                    );
                    return (
                        <div
                            key={`phone-${field.id}`}
                            style={{ position: 'relative' }}
                            className='d-flex gap-3 mb-3'
                        >
                            <FormController
                                controlType='textInput'
                                name={`associatedInfoDtos.${fieldIndex}.value`}
                                label=''
                                placeholder='Nhập số điện thoại khách hàng'
                                className='w-100'
                                required={index === 0}
                            />
                            <div
                                style={{
                                    position: 'absolute',
                                    right: '-38px',
                                    top: '5px',
                                    borderRadius: '50%',
                                    border:
                                        index === phoneFields.length - 1
                                            ? '1px solid #0ab39c'
                                            : '1px solid #dc3545',
                                    backgroundColor: '#ffffff',
                                    color:
                                        index === phoneFields.length - 1
                                            ? '#0ab39c'
                                            : '#dc3545',
                                    display: 'flex',
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    width: '25px',
                                    height: '25px',
                                    cursor: 'pointer',
                                }}
                            >
                                {index === phoneFields.length - 1 ? (
                                    <IconPlus
                                        size={18}
                                        stroke={1.5}
                                        onClick={() =>
                                            appendAssociatedInfo({
                                                value: '',
                                                associatedInfoType:
                                                    AssociatedInfoType.PhoneNumber,
                                            })
                                        }
                                    />
                                ) : (
                                    <IconX
                                        size={18}
                                        stroke={1.5}
                                        onClick={() =>
                                            removeAssociatedInfo(fieldIndex)
                                        }
                                    />
                                )}
                            </div>
                        </div>
                    );
                })
            ) : (
                <div style={{ position: 'relative' }}>
                    <FormController
                        controlType='textInput'
                        name='phone-placeholder'
                        label=''
                        placeholder='Nhập số điện thoại khách hàng'
                        className='w-100'
                    />
                    <div
                        style={{
                            position: 'absolute',
                            right: '-24px',
                            top: '8px',
                        }}
                    >
                        <IconPlus
                            size={18}
                            stroke={1.5}
                            onClick={() =>
                                appendAssociatedInfo({
                                    value: '',
                                    associatedInfoType:
                                        AssociatedInfoType.PhoneNumber,
                                })
                            }
                        />
                    </div>
                </div>
            )}
        </Col>
    );

    return (
        <CollapseApp title='THÔNG TIN CHÍNH'>
            <Row style={{ padding: '20px 40px 20px 40px' }}>
                <Col md='12' className='mb-3'>
                    <FormController
                        controlType='textInput'
                        name='name'
                        label='Tên khách hàng'
                        placeholder='Nhập tên khách hàng '
                        required={true}
                    />
                </Col>
                <Col md='12' className='mb-3'>
                    <FormController
                        controlType='textInput'
                        name='shortName'
                        label='Tên rút gọn'
                        placeholder='Nhập rút gọn của khách hàng'
                    />
                </Col>
                <Col md='6' style={{ paddingRight: '50px' }}>
                    <Col md='12' className='mb-3'>
                        <SelectBusinessTypeControl
                            name='businessTypeId'
                            label='Loại hình'
                            placeholder='Chọn loại hình doanh nghiệp'
                            style={{
                                width: '100%',
                            }}
                            clearable={true}
                            required={true}
                        />
                    </Col>
                    {renderEmailFields()}
                    <Col md='12' className='mb-3'>
                        <FormController
                            controlType='textInput'
                            name='taxCode'
                            label='Mã số thuế'
                            placeholder='Nhập mã số thuế của khách hàng'
                            required={true}
                        />
                    </Col>
                    <Col md='12' className='mb-3'>
                        <FormController
                            controlType='textInput'
                            type='number'
                            name='numberOfEmployees'
                            label='Số lượng nhân sự'
                            placeholder='Nhập số lượng nhân sự'
                        />
                    </Col>
                    <Col md='12' className='mb-3'>
                        <FormController
                            controlType='textInput'
                            name='website'
                            label='Website'
                            placeholder='Nhập link website của khách hàng'
                        />
                    </Col>
                    <Col md='12' className='mb-3'>
                        <FormController
                            controlType='textInput'
                            name='facebook'
                            label='Facebook'
                            placeholder='Nhập link Facebook của khách hàng'
                        />
                    </Col>
                    <Col md='12' className='mb-3'>
                        <FormController
                            controlType='select'
                            name='lifecycleStageEnum'
                            label='Giai đoạn'
                            placeholder='Chọn giai đoạn'
                            data={LifecycleStage}
                            required={true}
                        />
                    </Col>
                    <Col md='12' className='mb-3'>
                        <FormGroup>
                            <Label>
                                <strong>Ảnh đại diện</strong>
                            </Label>
                            <div className='relative inline-block'>
                                <div style={{ height: '60px' }}>
                                    <Avatar />
                                </div>
                            </div>
                        </FormGroup>
                    </Col>
                </Col>
                <Col md='6' style={{ paddingLeft: '50px' }}>
                    <Col md='12' className='mb-3'>
                        <SelectIndustryControl
                            name='industryId'
                            businessTypeId={businessTypeId ?? ''}
                            label='Lĩnh vực'
                            placeholder='Chọn lĩnh vực'
                            style={{ width: '100%' }}
                            clearable={true}
                            required={true}
                        />
                    </Col>
                    {renderPhoneFields()}
                    <Col md='12' className='mb-3'>
                        <FormController
                            controlType='textInput'
                            name='annualRevenue'
                            label='Doanh thu ước tính'
                            placeholder='Nhập doanh thu ước tính của khách hàng'
                            required={true}
                        />
                    </Col>
                    <Col md='12' className='mb-3'>
                        <FormController
                            controlType='textInput'
                            name='dateOfEstablishment'
                            label='Năm thành lập'
                            placeholder='Nhập năm thành lập'
                        />
                    </Col>
                    <Col md='12' className='mb-3'>
                        <FormController
                            controlType='textInput'
                            name='youtube'
                            label='Youtube'
                            placeholder='Nhập link Youtube của khách hàng'
                        />
                    </Col>
                    <Col md='12' className='mb-3'>
                        <FormController
                            controlType='textInput'
                            name='linkedInPage'
                            label='LinkedIn page'
                            placeholder='Nhập link LinkedIn của khách hàng'
                        />
                    </Col>
                    <Col md='12' className='mb-3'>
                        <FormController
                            controlType='select'
                            name='leadStatus'
                            label='Trạng thái'
                            placeholder='Chọn trạng thái'
                            data={LeadStatus}
                            required={true}
                        />
                    </Col>
                </Col>
            </Row>
        </CollapseApp>
    );
};

export default MainInfo;
