import { <PERSON><PERSON>, <PERSON>, Card<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from 'reactstrap';

import { ICustomer } from '@/apis/customer/customer.type';
import BoxAddInfo from '@/components/common/BoxAddInfo';

interface FileInfoProps {
    data: ICustomer;
}
const FileInfo = ({ data }: FileInfoProps) => {
    const handleCreate = () => {};
    return (
        <Col lg={3}>
            <Card className='mb-3'>
                <CardHeader className='border-bottom-0'>
                    <div className='d-flex align-items-center'>
                        <h5 className='mb-0 flex-grow-1'>File đính kèm (2)</h5>
                        <Button
                            color='success'
                            size='sm'
                            className='btn-sm'
                            style={{
                                backgroundColor: '#0ab39c',
                                border: 'none',
                            }}
                        >
                            <i className='ri-add-line align-middle'></i> Thêm
                        </Button>
                    </div>
                </CardHeader>
                <CardBody>
                    {[
                        {
                            name: 'CRM.docx',
                            icon: 'ri-file-text-line',
                            date: '11/01/2025',
                        },
                        {
                            name: 'CRM.ppt',
                            icon: 'ri-file-ppt-line',
                            date: '11/01/2025',
                        },
                    ].map((file, index) => (
                        <div className='mb-3' key={index}>
                            <div className='d-flex align-items-center mb-2'>
                                <i
                                    className={`${file.icon} text-primary fs-18 me-2`}
                                ></i>
                                <div className='flex-grow-1'>
                                    <h6 className='mb-0'>{file.name}</h6>
                                    <small className='text-muted'>
                                        Ngày tải lên: {file.date}
                                    </small>
                                </div>
                                <div className='d-flex'>
                                    <Button
                                        color='light'
                                        size='sm'
                                        className='btn-icon me-1'
                                    >
                                        <i className='ri-download-line'></i>
                                    </Button>
                                    <Button
                                        color='light'
                                        size='sm'
                                        className='btn-icon'
                                    >
                                        <i className='ri-delete-bin-line'></i>
                                    </Button>
                                </div>
                            </div>
                        </div>
                    ))}

                    <Button color='info' outline block>
                        Xem thêm
                    </Button>
                </CardBody>
            </Card>
            <BoxAddInfo
                title='Đối tác thương mại'
                length={data.detailTradePartnerCompanies.length || 0}
                content={data.detailTradePartnerCompanies.map((item) => ({
                    title: item.name,
                    data: [
                        {
                            label: 'Email',
                            value: item.email,
                            boxColor: false,
                        },
                        {
                            label: 'Số điện thoại',
                            value: item.phoneNumber,
                            boxColor: false,
                        },
                    ],
                    // onViewQuick: item.onViewQuick,
                    // onViewDetail: item.onViewDetail,
                    // onDelete: item.onDelete,
                }))}
                // onAdd={handleAddNewItem}
            />

            <BoxAddInfo
                title='Phòng mua hàng'
                length={data.purchaseContactDetailCompanies.length || 0}
                content={data.purchaseContactDetailCompanies.map((item) => ({
                    title: item.name,
                    data: [
                        {
                            label: 'Email',
                            value: item.email,
                            boxColor: false,
                        },
                        {
                            label: 'Số điện thoại',
                            value: item.phoneNumber,
                            boxColor: false,
                        },
                    ],
                }))}
                onAdd={handleCreate}
            />

            <BoxAddInfo
                title='Phòng sử dụng'
                length={data.usageContactDetailCompanies.length || 0}
                content={data.usageContactDetailCompanies.map((item) => ({
                    title: item.name,
                    data: [
                        {
                            label: 'Email',
                            value: item.email,
                            boxColor: false,
                        },
                        {
                            label: 'Số điện thoại',
                            value: item.phoneNumber,
                            boxColor: false,
                        },
                    ],
                }))}
                onAdd={handleCreate}
            />
        </Col>
    );
};

export default FileInfo;
