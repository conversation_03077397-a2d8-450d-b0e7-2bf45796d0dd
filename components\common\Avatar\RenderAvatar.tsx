import React from 'react';

interface Props {
    value?: string;
}

const RenderAvatarComponent = (props?: Props) => {
    const { value = 'Name' } = props || {};

    if (!value?.trim()) {
        return null;
    }

    const initial = value.charAt(0).toUpperCase();
    return (
        <div className='position-relative d-inline-block'>
            <div
                className='d-flex justify-content-center align-items-center rounded-circle fw-bold'
                style={{
                    width: 60,
                    height: 60,
                    backgroundColor: '#daf4f0',
                    color: '#0ab39c',
                    fontSize: 20,
                }}
            >
                {initial}
            </div>
            <span
                className='position-absolute'
                style={{
                    right: 0,
                    bottom: 0,
                    width: 12,
                    height: 12,
                    backgroundColor: '#28a745',
                    borderRadius: '50%',
                    border: '2px solid white',
                }}
            />
        </div>
    );
};

const RenderAvatar = React.memo(RenderAvatarComponent);
RenderAvatar.displayName = 'RenderAvatar';

export default RenderAvatar;
