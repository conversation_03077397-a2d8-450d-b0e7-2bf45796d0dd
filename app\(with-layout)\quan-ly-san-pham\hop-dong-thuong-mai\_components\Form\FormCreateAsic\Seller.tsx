import CollapseApp from '@/components/common/CollapseApp';
import FormController from '@/components/common/FormController';
import ComboboxSelectUserControl from '@/components/common/FormController/ComboboxSelectUserControl';
import SelectPositionControl from '@/components/common/FormController/SelectPositionControl';
import { useGetUsersSearch } from '@/apis/users/users.api';
import useGetOptionsPosition from '@/hooks/useGetOptionsPosition';
import { useFormContext } from 'react-hook-form';
import { Col, Row } from 'reactstrap';
import { useMemo, useEffect } from 'react';

const Seller = () => {
    const { watch, setValue } = useFormContext();
    const sellerStaffId = watch('seller.staffId');
    const sellerPositionId = watch('seller.positionId');

    const { data: users = [] } = useGetUsersSearch({
        name: '',
    });

    const { positions = [] } = useGetOptionsPosition();

    const sellerStaffname = useMemo(() => {
        if (!sellerStaffId || !users.length) {
            return '';
        }
        const selectedUser = users.find(
            (user) => user.userId === sellerStaffId,
        );
        return selectedUser?.userName || '';
    }, [sellerStaffId, users]);

    const sellerPositionName = useMemo(() => {
        if (!sellerPositionId || !positions.length) {
            return '';
        }
        const selectedPosition = positions.find(
            (position) => position.value === sellerPositionId,
        );
        return selectedPosition?.label || '';
    }, [sellerPositionId, positions]);

    useEffect(() => {
        if (sellerStaffname) {
            setValue('seller.staffName', sellerStaffname);
        }
    }, [sellerStaffname, setValue]);

    useEffect(() => {
        if (sellerPositionName) {
            setValue('seller.position', sellerPositionName);
        }
    }, [sellerPositionName, setValue]);

    return (
        <CollapseApp title='BÊN BÁN'>
            <div style={{ padding: '20px 40px 20px 40px' }}>
                <Row>
                    <Col
                        md={6}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <FormController
                            controlType='textInput'
                            name='seller.bankName'
                            label='Ngân hàng'
                            placeholder='Nhập ngân hàng'
                            required={true}
                        />
                    </Col>
                    <Col
                        md={6}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <FormController
                            controlType='textInput'
                            name='seller.bankBranch'
                            label='Chi nhánh'
                            placeholder='Nhập chi nhánh'
                            required={true}
                        />
                    </Col>
                    <Col
                        md={6}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <FormController
                            controlType='textInput'
                            name='seller.bankAccount'
                            label='Số tài khoản'
                            placeholder='Nhập số tài khoản'
                            required={true}
                        />
                    </Col>
                    <Col
                        md={3}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <ComboboxSelectUserControl
                            name='seller.staffId'
                            label='Người đại diện'
                            placeholder='Chọn người đại diện'
                            style={{
                                width: '100%',
                            }}
                            required={true}
                        />

                        <FormController
                            controlType='textInput'
                            name='seller.staffName'
                            style={{ display: 'none' }}
                        />
                    </Col>
                    <Col
                        md={3}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <SelectPositionControl
                            name='seller.positionId'
                            label='Chức vụ'
                            placeholder='Chọn chức vụ'
                            required={true}
                            style={{
                                width: '100%',
                            }}
                        />
                        <FormController
                            controlType='textInput'
                            name='seller.position'
                            style={{ display: 'none' }}
                        />
                    </Col>
                </Row>
            </div>
        </CollapseApp>
    );
};
export default Seller;
