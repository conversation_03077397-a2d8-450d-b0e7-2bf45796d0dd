/* Kanban Column Styles */
.kanbanColumn {
    min-width: 280px;
    max-width: 280px;
    display: flex;
    flex-direction: column;
    height: calc(100vh - 285px);
}

.kanbanItemsContainer {
    overflow-y: auto !important;
    overflow-x: hidden !important;
    min-height: 100px;
    max-height: calc(100vh - 365px);
    border-radius: 4px;
    margin-bottom: 0;
    flex: 1;
    padding-right: 4px;
    transition: background-color 0.2s ease;
}

/* Webkit browsers (Chrome, Safari, Edge) */
.kanbanItemsContainer::-webkit-scrollbar {
    width: 8px;
}

.kanbanItemsContainer::-webkit-scrollbar-track {
    background: transparent;
}

.kanbanItemsContainer::-webkit-scrollbar-thumb {
    background-color: transparent;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

/* Show scrollbar on hover */
.kanbanColumn:hover .kanbanItemsContainer::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.3);
}

.kanbanColumn:hover .kanbanItemsContainer::-webkit-scrollbar-thumb:hover {
    background-color: rgba(0, 0, 0, 0.5);
}

/* Firefox */
.kanbanItemsContainer {
    scrollbar-width: thin;
    scrollbar-color: transparent transparent;
}

.kanbanColumn:hover .kanbanItemsContainer {
    scrollbar-color: rgba(0, 0, 0, 0.3) transparent;
}

.totalRevenue {
    width: 100%;
    height: 40px;
    background: rgba(223, 227, 235, 1);
    flex-shrink: 0;
    position: sticky;
    bottom: 0;
}
