import { IProductDisplayDeal, IQuotePayload } from '@/apis/quotes/quotes.type';
import { DealType } from '@/app/(with-layout)/crm/co-hoi-ban-hang/types';
import FormController from '@/components/common/FormController';
import { formatDisplayNumber } from '@/utils/format';
import { useFieldArray, useFormContext, useWatch } from 'react-hook-form';

interface InfoProductProps {
    index: number;
    key: string;
    item: IProductDisplayDeal;
    isEdit?: boolean;
    dealType: DealType;
}

const renderStyle = (width: number, fontWeight = 600) => {
    const css: Record<string, string | number> = {
        fontSize: '16px',
        fontWeight,
    };
    if (width) {
        css.width = width + 'px';
    }

    return css;
};

// Helper function to handle large numbers and prevent Infinity
const safeCalculate = (value: number, maxValue = Number.MAX_SAFE_INTEGER) => {
    if (!isFinite(value) || isNaN(value)) {
        return 0;
    }
    if (value > maxValue) {
        return maxValue;
    }
    if (value < -maxValue) {
        return -maxValue;
    }
    return value;
};

// Helper function for division that prevents Infinity
const safeDivision = (numerator: number, denominator: number, fallback = 0) => {
    if (
        denominator === 0 ||
        !isFinite(denominator) ||
        Math.abs(denominator) < 0.0001
    ) {
        return fallback;
    }
    const result = numerator / denominator;
    return safeCalculate(result);
};

const RowInfoProduct = (props: InfoProductProps) => {
    const { index, key, item, isEdit, dealType } = props;

    const methods = useFormContext<IQuotePayload>();
    const { control } = methods;

    const formData = useWatch({ control });

    const exchangeRate = Number(formData?.exchangeRate || 0);
    const serviceFee = Number(formData?.items?.[index]?.serviceFee || 0);
    const importTaxPercent = Number(
        formData?.items?.[index]?.importTaxPercent || 0,
    );
    const extendedDiscountRequest = Number(
        formData?.items?.[index]?.extendedDiscountRequest || 0,
    );
    const fctTax = Number(formData?.items?.[index]?.fctTax || 0);
    const quantity = Number(formData?.items?.[index]?.quantity || 0);
    const markUp = Number(formData?.items?.[index]?.markUp || 0);

    const listPrice = Number(item.listPrice);
    const standardDiscount = Number(item.standardDiscount);

    const totalDiscount = safeCalculate(
        standardDiscount + extendedDiscountRequest,
    );
    const netPrice = safeCalculate(listPrice * totalDiscount);
    const totalNetPrice = safeCalculate(netPrice * quantity);
    const importTax = safeCalculate(importTaxPercent * netPrice);
    const totalImportTax = safeCalculate(importTax * quantity);
    const unitFctTax = safeDivision(netPrice, 1 - fctTax * fctTax, 0);
    const totalFctTax = safeCalculate(unitFctTax * quantity);
    const unitPriceIncludingTax = safeCalculate(
        netPrice + importTax + unitFctTax,
    );
    const totalIncludingTax = safeCalculate(unitPriceIncludingTax * quantity);

    let unitPrice = safeDivision(listPrice, 1 - markUp, listPrice);
    if (listPrice < unitPriceIncludingTax) {
        unitPrice = safeDivision(
            unitPriceIncludingTax,
            1 - markUp,
            unitPriceIncludingTax,
        );
    }

    unitPrice = safeCalculate(Math.ceil(unitPrice));

    const totalPrice = safeCalculate(unitPrice * quantity);
    const totalPricePerSet = safeCalculate(
        totalPrice * exchangeRate + serviceFee,
    );

    if (isEdit) {
        return (
            <tr>
                <td>{index + 1}</td>
                <td>{item.modelCode}</td>
                {dealType === DealType.Classic && (
                    <td style={renderStyle(150)}>{item.optionCode}</td>
                )}
                <td>{item.description}</td>
                <td>{item.productType}</td>
                <td>
                    <FormController
                        controlType='numberInput'
                        name={`items.${index}.serviceFee`}
                        placeholder='Nhập giá'
                        style={{ width: '100%' }}
                        thousandSeparator=','
                        decimalSeparator='.'
                        decimalScale={2}
                        step={0.01}
                    />
                </td>
                <td>{formatDisplayNumber(listPrice)}</td>
                <td>
                    <FormController
                        controlType='numberInput'
                        name={`items.${index}.quantity`}
                        placeholder='Nhập số lượng'
                        style={{ width: '100%' }}
                    />
                </td>
                <td>{formatDisplayNumber(item.standardDiscount)}</td>
                <td>
                    <FormController
                        controlType='numberInput'
                        name={`items.${index}.extendedDiscountRequest`}
                        placeholder='Nhập %'
                        style={{ width: '100%' }}
                        suffix='%'
                    />
                </td>
                <td>{formatDisplayNumber(totalDiscount)}</td>
                <td>{formatDisplayNumber(netPrice)}</td>
                <td>{formatDisplayNumber(totalNetPrice)}</td>
                <td>
                    <FormController
                        controlType='numberInput'
                        name={`items.${index}.importTaxPercent`}
                        placeholder='Nhập %'
                        style={{ width: '100%' }}
                        suffix='%'
                    />
                </td>
                <td>{formatDisplayNumber(importTax)}</td>
                <td>{formatDisplayNumber(totalImportTax)}</td>
                <td>
                    <FormController
                        controlType='numberInput'
                        name={`items.${index}.fctTax`}
                        placeholder='Nhập %'
                        style={{ width: '100%' }}
                        suffix='%'
                    />
                </td>
                <td>{formatDisplayNumber(unitFctTax)}</td>
                <td>{formatDisplayNumber(totalFctTax)}</td>
                <td>{formatDisplayNumber(unitPriceIncludingTax)}</td>
                <td>{formatDisplayNumber(totalIncludingTax)}</td>
                <td>
                    <FormController
                        controlType='numberInput'
                        name={`items.${index}.markUp`}
                        placeholder='Nhập %'
                        style={{ width: '100%' }}
                        suffix='%'
                    />
                </td>
                <td>{formatDisplayNumber(unitPrice)}</td>
                <td>{formatDisplayNumber(totalPrice)}</td>
                <td>{formatDisplayNumber(totalPricePerSet)}</td>
            </tr>
        );
    }

    return (
        <tr key={key}>
            <td>STT</td>
            <td>Model code</td>
            {dealType === DealType.Classic && <td>Option code</td>}
            <td>Description</td>
            <td>Subcription type</td>
            <td>Service fee</td>
            <td>List price</td>
            <td>Quantity</td>
            <td>Standard Discount</td>
            <td>Ex. dist. Request (%)</td>
            <td>Total Discount (%)</td>
            <td>NET price (USD)</td>
            <td>Total NET price (USD)</td>
            <td>IMPORT Tax (%)</td>
            <td>IMPORT Tax</td>
            <td>Total IMPORT Tax</td>
            <td>FCT tax</td>
            <td>Unit FCT Tax</td>
            <td>Total FCT Tax</td>
            <td>Unit Price INCLUDING Tax (USD)</td>
            <td>Mark up</td>
            <td>Unit Price (USD)</td>
            <td>Total Price (USD)</td>
            <td>Đơn giá (VND)</td>
            <td>Tổng giá theo bộ (VND)</td>
        </tr>
    );
};

interface TableInfoProductProps {
    dealType: DealType;
}

const TableInfoProduct = (props: TableInfoProductProps) => {
    const { dealType } = props;

    const methods = useFormContext<IQuotePayload>();
    const { control } = methods;

    const { fields: items } = useFieldArray({
        control,
        name: 'items',
    });

    const watchedItems = useWatch({
        control,
        name: 'items',
        defaultValue: [],
    });

    const renderInfoProducts = () => (
        <table
            className='table table-bordered mb-0'
            style={{ tableLayout: 'fixed', width: '100%' }}
        >
            <thead>
                <tr>
                    <th style={renderStyle(70)}>STT</th>
                    <th style={renderStyle(135)}>Model code</th>
                    {dealType === DealType.Classic && (
                        <th style={renderStyle(150)}>Option code</th>
                    )}
                    <th style={renderStyle(180)}>Description</th>
                    <th style={renderStyle(150)}>Subcription type</th>
                    <th style={renderStyle(150)}>Service fee</th>
                    <th style={renderStyle(100)}>List price</th>
                    <th style={renderStyle(160)}>Quantity</th>
                    <th style={renderStyle(170)}>Standard Discount</th>
                    <th style={renderStyle(200)}>Ex. dist. Request (%)</th>
                    <th style={renderStyle(150)}>Total Discount</th>
                    <th style={renderStyle(150)}>NET price (USD)</th>
                    <th style={renderStyle(190)}>Total NET price (USD)</th>
                    <th style={renderStyle(150)}>IMPORT Tax (%)</th>
                    <th style={renderStyle(120)}>IMPORT Tax</th>
                    <th style={renderStyle(160)}>Total IMPORT Tax</th>
                    <th style={renderStyle(130)}>FCT tax</th>
                    <th style={renderStyle(120)}>Unit FCT Tax</th>
                    <th style={renderStyle(130)}>Total FCT Tax</th>
                    <th style={renderStyle(300)}>
                        Unit Price INCLUDING Tax (USD)
                    </th>
                    <th style={renderStyle(220)}>Total Including Tax (USD)</th>
                    <th style={renderStyle(130)}>Mark up</th>
                    <th style={renderStyle(170)}>Unit Price (USD)</th>
                    <th style={renderStyle(155)}>Total Price (USD)</th>
                    <th style={renderStyle(210)}>Tổng giá theo bộ (VND)</th>
                </tr>
            </thead>
            <tbody>
                {items.length === 0 ? (
                    <tr>
                        <td colSpan={11} className='text-center py-3'>
                            Chưa có dữ liệu về sản phẩm
                        </td>
                    </tr>
                ) : (
                    <>
                        {items.map((_, index) => (
                            <RowInfoProduct
                                index={index}
                                key={`item-${index}`}
                                item={
                                    watchedItems[index] ||
                                    ({} as IProductDisplayDeal)
                                }
                                isEdit={true}
                                dealType={dealType}
                            />
                        ))}
                        <tr>
                            <td
                                colSpan={11}
                                style={renderStyle(0)}
                                className='text-center py-3'
                            >
                                Total NET price
                            </td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                        </tr>
                        <tr>
                            <td
                                colSpan={11}
                                style={renderStyle(0)}
                                className='text-center py-3'
                            >
                                Discount to customer
                            </td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                        </tr>
                        <tr>
                            <td
                                colSpan={11}
                                style={renderStyle(0)}
                                className='text-center py-3'
                            >
                                Final total CIP price
                            </td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                        </tr>
                    </>
                )}
            </tbody>
        </table>
    );

    return (
        <div className='mb-4'>
            <label style={{ fontWeight: 500, fontSize: '14px' }}>
                Thông tin sản phẩm
            </label>
            <div
                className='table-scroll-container'
                style={{ maxWidth: '100%', overflowX: 'auto' }}
            >
                {renderInfoProducts()}
            </div>
            <style jsx>{`
                .table-scroll-container::-webkit-scrollbar {
                    height: 8px;
                }

                .table-scroll-container::-webkit-scrollbar-track {
                    background: transparent;
                }

                .table-scroll-container::-webkit-scrollbar-thumb {
                    background-color: rgba(0, 0, 0, 0.2);
                    border-radius: 4px;
                }

                .table-scroll-container::-webkit-scrollbar-button {
                    display: none;
                    width: 0;
                    height: 0;
                }

                .table-scroll-container::-webkit-scrollbar-button:start:decrement,
                .table-scroll-container::-webkit-scrollbar-button:end:increment {
                    display: none;
                    width: 0;
                    height: 0;
                }

                .table-scroll-container::-webkit-scrollbar-corner {
                    background: transparent;
                }

                .table-scroll-container {
                    scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
                    scrollbar-width: thin;
                }
            `}</style>
        </div>
    );
};

export default TableInfoProduct;
