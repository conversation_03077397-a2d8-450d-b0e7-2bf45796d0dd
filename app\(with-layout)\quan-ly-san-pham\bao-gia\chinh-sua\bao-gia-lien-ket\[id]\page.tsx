'use client';

import { useGetQuoteDetail, useUpdateQuote } from '@/apis/quotes/quotes.api';
import { IQuotePayload } from '@/apis/quotes/quotes.type';
import { KEYS_TO_QUOTE } from '@/constants/key-convert';
import { ROUTES } from '@/lib/routes';
import { convertFormValueToPayload } from '@/utils/convert-data';
import { showToastSuccess } from '@/utils/toast-message';
import { useParams, useRouter } from 'next/navigation';
import { useMemo } from 'react';
import { toast } from 'react-toastify';
import LinkQuoteForm from '../../../_components/LinkQuoteForm';

const UpdateLinkQuote = () => {
    const router = useRouter();
    const params = useParams();
    const id = params.id as string;

    const { data } = useGetQuoteDetail({ id }, { enabled: !!id });

    const quoteDetail = useMemo(() => {
        if (data?.data) {
            return {
                id: data.data?.id ?? '',
                name: data.data.name ?? '',
                quoteTemplate: data.data?.quoteTemplate ?? '',
                requiresApproval: data.data?.requiresApproval ?? false,
                description: data.data?.description ?? '',
                dealId: data.data?.dealId ?? '',
                discountToCustomer: data.data?.discountToCustomer ?? '',
                expectedEndDate: data.data?.expectedEndDate ?? '',
                exchangeRate: data.data?.exchangeRate ?? 0,
                items: data.data?.quoteItems ?? [],
            };
        }

        return {};
    }, [data?.data]);

    const { mutate: updateQuote } = useUpdateQuote({
        onSuccess: () => {
            showToastSuccess({
                title: 'Cập nhật thông tin báo giá thành công',
                message:
                    'Thông tin báo giá đã được cập nhật thành công trong hệ thống.',
            });

            router.push(ROUTES.PRODUCT_MANAGEMENT.QUOTES.INDEX);
        },
        onError: (error) => {
            toast.error(
                <div className='flex flex-columns gap-1'>
                    <p className='p-0 m-0 mt-3'>Sửa báo giá thất bại</p>
                    <p className='p-0 m-0'>{error.detail}</p>
                </div>,
            );
        },
    });

    const handleUpdateQuote = (data: IQuotePayload) => {
        if (data.customer) {
            delete data.customer;
        }
        if (data.partner) {
            delete data.partner;
        }

        if (data.expectedEndDate) {
            data.expectedEndDate = data.expectedEndDate.replace(' ', 'T');
        }

        const payload = convertFormValueToPayload(data, KEYS_TO_QUOTE);

        updateQuote(payload);
    };

    const handleCancel = () => {
        router.push(ROUTES.PRODUCT_MANAGEMENT.QUOTES.INDEX);
    };

    return (
        <LinkQuoteForm
            initValue={quoteDetail as IQuotePayload}
            onSubmit={handleUpdateQuote}
            onCancel={handleCancel}
        />
    );
};

export default UpdateLinkQuote;
