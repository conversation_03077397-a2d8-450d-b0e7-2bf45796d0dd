// Helper function to format display numbers
export const formatDisplayNumber = (value: number, decimals = 2) => {
    if (!isFinite(value) || isNaN(value)) {
        return '0';
    }

    // If number is too large, display in scientific notation or with abbreviation
    if (Math.abs(value) >= 1e12) {
        return (value / 1e12).toFixed(1) + 'T'; // Trillion
    }
    if (Math.abs(value) >= 1e9) {
        return (value / 1e9).toFixed(1) + 'B'; // Billion
    }
    if (Math.abs(value) >= 1e6) {
        return (value / 1e6).toFixed(1) + 'M'; // Million
    }

    return value.toLocaleString('vi-VN', {
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals,
    });
};
