import { IQuoteTemplateDetail } from '@/apis/quotes/quote-templates.type';
import FormController from '@/components/common/FormController';
import { useState } from 'react';
import { useFormContext, useWatch } from 'react-hook-form';
import { Col } from 'reactstrap';
import { fieldTypeOptions } from '../../type';
import ModalSetUpCalculationFormula from './ModalSetUpCalculationFormula';

interface Props {
    index: number;
    onRemove: (index: number) => void;
    isEnabledRemove: boolean;
    readOnly: boolean;
}

const ConfigurationRow = ({
    index,
    onRemove,
    isEnabledRemove,
    readOnly,
}: Props) => {
    const methods = useFormContext<IQuoteTemplateDetail>();
    const { control, setValue } = methods;

    const nameFieldCode = `quoteTemplateFields.${index}.fieldCode`;
    const nameFieldName = `quoteTemplateFields.${index}.fieldName`;
    const nameDataType = `quoteTemplateFields.${index}.dataType`;
    const nameDescription = `quoteTemplateFields.${index}.description`;
    const nameIsVisible =
        `quoteTemplateFields.${index}.isVisible` as `quoteTemplateFields.${number}.isVisible`;

    const isVisible = useWatch({
        control,
        name: nameIsVisible,
    });

    const [isOpenModalCalculationFormula, setIsOpenModalCalculationFormula] =
        useState(false);

    const handleToggle = () => {
        setIsOpenModalCalculationFormula(!isOpenModalCalculationFormula);
    };

    return (
        <>
            <Col md='11'>
                <div className='row-quotation-form d-flex justify-content-between'>
                    <div style={{ width: '146px' }}>
                        <FormController
                            controlType='textInput'
                            name={nameFieldCode}
                            placeholder='Nhập mã'
                            maxLength={256}
                            readOnly={readOnly}
                        />
                    </div>
                    <div style={{ width: '203px' }}>
                        <FormController
                            controlType='textInput'
                            name={nameFieldName}
                            placeholder='Nhập một trường'
                            maxLength={256}
                            readOnly={readOnly}
                        />
                    </div>
                    <div style={{ width: '220px' }}>
                        <FormController
                            controlType='select'
                            name={nameDataType}
                            placeholder='Chọn kiểu dữ liệu'
                            data={fieldTypeOptions}
                            readOnly={readOnly}
                        />
                    </div>
                    <div style={{ width: '320px' }}>
                        <FormController
                            controlType='textInput'
                            name={nameDescription}
                            placeholder='Nhập mô tả'
                            maxLength={512}
                            readOnly={readOnly}
                        />
                    </div>
                    <div
                        style={{ width: '60px' }}
                        className='d-flex justify-content-center align-items-center'
                        onClick={() => {
                            if (!readOnly) {
                                setValue(nameIsVisible, !isVisible);
                            }
                        }}
                    >
                        <i
                            className={`${isVisible ? 'ri-eye-line' : 'ri-eye-off-line'} align-bottom me-1`}
                            style={{
                                fontSize: '1.25rem',
                                fontWeight: '400',
                                cursor: !readOnly ? 'pointer' : 'default',
                            }}
                        ></i>
                    </div>
                    <div
                        style={{
                            width: '180px',
                            cursor: 'pointer',
                        }}
                        className='d-flex align-items-center'
                        onClick={handleToggle}
                    >
                        <i
                            className='ri-tools-fill align-bottom me-1'
                            style={{
                                fontSize: '1.25rem',
                                fontWeight: '400',

                                color: 'rgba(10, 179, 156, 1)',
                            }}
                        ></i>
                        <p
                            style={{
                                fontWeight: 700,
                                color: 'rgba(10, 179, 156, 1)',
                            }}
                            className='mb-0'
                        >
                            Thiết lập công thức tính
                        </p>
                    </div>

                    {!readOnly && (
                        <div
                            style={{
                                fontWeight: 700,
                                width: '16px',
                                cursor: isEnabledRemove
                                    ? 'pointer'
                                    : 'not-allowed',
                            }}
                            className='d-flex align-items-center'
                        >
                            <i
                                className='ri-delete-bin-line mb-1'
                                style={{
                                    fontSize: '1.25rem',
                                    fontWeight: '400',
                                    color: `rgba(236, 66, 67, ${isEnabledRemove ? 1 : 0.4})`,
                                }}
                                onClick={() => {
                                    if (isEnabledRemove) {
                                        onRemove(index);
                                    }
                                }}
                            ></i>
                        </div>
                    )}
                </div>
            </Col>
            <ModalSetUpCalculationFormula
                isOpen={isOpenModalCalculationFormula}
                onToggle={handleToggle}
                readOnly={readOnly}
            />
        </>
    );
};

export default ConfigurationRow;
