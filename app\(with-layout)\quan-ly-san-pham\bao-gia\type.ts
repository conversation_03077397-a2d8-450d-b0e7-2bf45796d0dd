import { Option } from '@/types/app.type';

export enum TabValues {
    QUOTATION_FORM = 'QUOTATION_FORM',
    LINK_QUOTE = 'LINK_QUOTE',
}

export enum Status {
    QUOTE = 'QUOTE',
    SENDED = 'SENDED',
    HAS_REFUSED = 'HAS_REFUSED',
    CONFIRMED = 'CONFIRMED',
}

export const statusOptions: Option[] = [
    {
        label: 'Báo giá',
        value: Status.QUOTE,
    },
    {
        label: 'Đã gửi',
        value: Status.SENDED,
    },
    {
        label: 'Đã từ chối',
        value: Status.HAS_REFUSED,
    },
    {
        label: 'Đã xác nhận',
        value: Status.CONFIRMED,
    },
];

export enum FieldType {
    DOCUMENT = 'document',
    NUMBER = 'number',
    CURRENCY = 'currency',
    DATE = 'date',
    TIME = 'time',
    PERCENT = 'percent',
    FRACTION = 'fraction',
    PHONE_NUMBER = 'phone_number',
}

export const fieldTypeOptions: Option[] = [
    {
        label: 'Văn bản',
        value: FieldType.DOCUMENT,
    },
    {
        label: 'Số',
        value: FieldType.NUMBER,
    },
    {
        label: 'Tiền tệ',
        value: FieldType.CURRENCY,
    },
    {
        label: 'Chọn ngày',
        value: FieldType.DATE,
    },
    {
        label: 'Chọn giờ',
        value: FieldType.TIME,
    },
    {
        label: 'Phần trăm',
        value: FieldType.PERCENT,
    },
    {
        label: 'Phân số',
        value: FieldType.FRACTION,
    },
    {
        label: 'Số điện thoại',
        value: FieldType.PHONE_NUMBER,
    },
];

export const linkQuoteTemplateOptions = [
    { label: 'Báo giá kiểm thử xâm nhập', value: 'penetration_testing' },
    {
        label: 'Báo giá giải pháp an ninh mạng',
        value: 'cybersecurity_solutions',
    },
    { label: 'Báo giá điện-điện tử rada', value: 'radar_systems' },
];

export interface TypeFormCustomerAndParter {
    companyName: string | null;
    companyId: string | null;
    tradePartnerId: string | null;
    tradePartnerName: string | null;
}

export enum QuotesType {
    INTERNAL = 1,
    EXTERNAL = 2,
}

export const QuotesTypeName = {
    [QuotesType.INTERNAL]: 'Báo giá nội bộ',
    [QuotesType.EXTERNAL]: 'Báo giá gửi khách hàng',
};

export const QuotesOptions = [
    {
        label: 'Báo giá nội bộ',
        value: QuotesType.INTERNAL,
    },
    {
        label: 'Báo giá gửi khách hàng',
        value: QuotesType.EXTERNAL,
    },
];

export enum ReturnDataType {
    DECIMAL, // Số thập phân
    PERCENT, // Số phần trăm
    TIME, // Ngày/ tháng/ năm/ giờ/ phút
}

export const returnDataTypeOptions: Option[] = [
    {
        value: ReturnDataType.DECIMAL.toString(),
        label: 'Số thập phân',
    },
    {
        value: ReturnDataType.PERCENT.toString(),
        label: 'Số phần trăm',
    },
    {
        value: ReturnDataType.TIME.toString(),
        label: 'Ngày/ tháng/ năm/ giờ/ phút',
    },
];
