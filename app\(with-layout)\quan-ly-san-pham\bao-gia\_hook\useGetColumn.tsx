import { IQuoteTempalteListResponse } from '@/apis/quotes/quote-templates.type';
import { IQuoteReponse } from '@/apis/quotes/quotes.type';
import DropdownActionMenu, {
    DropdownAction,
} from '@/components/common/DropdownActionMenu';
import FormattedDateTimeWithFormat from '@/components/common/FormattedDateTimeWithFormat';
import columnActionsDefault from '@/components/common/MantineReactTable/columnActionsDefault';
import columnDateDefault from '@/components/common/MantineReactTable/columnDateDefault';
import { ACTIONS } from '@/types/actions.type';
import { MRT_ColumnDef } from 'mantine-react-table';
import { useMemo } from 'react';
import { TabValues } from '../type';

interface GetColumnProps {
    onSelectedAction?: (
        action: ACTIONS,
        data: IQuoteReponse | IQuoteTempalteListResponse,
    ) => void;
    onRestore?: (data: IQuoteReponse | IQuoteTempalteListResponse) => void;
    page: string;
    tab?: TabValues;
}
const useGetColumn = ({
    onSelectedAction,
    onRestore,
    page,
    tab,
}: GetColumnProps) => {
    const actions: DropdownAction<
        IQuoteReponse | IQuoteTempalteListResponse
    >[] = useMemo(
        () => [
            {
                icon: 'ri-eye-line',
                label: 'Xem chi tiết',
                onClick: (data) =>
                    onSelectedAction?.(
                        ACTIONS.VIEW_DETAIL,
                        data as IQuoteReponse | IQuoteTempalteListResponse,
                    ),
            },
            {
                icon: 'ri-edit-line',
                label: 'Sửa báo giá',
                onClick: (data) =>
                    onSelectedAction?.(
                        ACTIONS.EDIT,
                        data as IQuoteReponse | IQuoteTempalteListResponse,
                    ),
            },
            {
                icon: 'ri-delete-bin-line',
                label: 'Xóa báo giá',
                onClick: (data) =>
                    onSelectedAction?.(
                        ACTIONS.DELETE,
                        data as IQuoteReponse | IQuoteTempalteListResponse,
                    ),
                className: 'text-danger',
            },
        ],
        [onSelectedAction],
    );
    const allColumns = useMemo<
        MRT_ColumnDef<IQuoteReponse | IQuoteTempalteListResponse>[]
    >(
        () => [
            {
                accessorKey: 'quoteName',
                header: 'Tên báo giá',
                enableSorting: false,
            },
            {
                accessorKey: 'name',
                header: 'Tên báo giá',
                enableSorting: false,
                size: 600,
            },
            { accessorKey: 'dealName', header: 'Tên cơ hội' },
            {
                accessorKey: 'finalTotalPrice',
                header: 'Thành tiền',
                size: 150,
                Cell: ({ row }) =>
                    (row.original as IQuoteReponse).finalTotalPrice,
            },
            {
                accessorKey: 'quotesTypeName',
                header: 'Loại báo giá',
            },
            {
                accessorKey: 'userNameCreated',
                header:
                    tab === TabValues.QUOTATION_FORM
                        ? 'Người tạo'
                        : 'Nhân viên kinh doanh',
                size: 180,
            },
            {
                accessorKey: 'statusName',
                header: 'Trạng thái',
                enableResizing: false,
                enableGrouping: false,
                enableSorting: false,
                Cell: ({ row }) => (row.original as IQuoteReponse).statusName,
                size: 100,
            },
            {
                ...(columnDateDefault as MRT_ColumnDef<
                    IQuoteReponse | IQuoteTempalteListResponse
                >),
                accessorKey: 'createdDateTime',
                header: 'Ngày tạo',
                Cell: ({ row }) => (
                    <FormattedDateTimeWithFormat
                        date={row.original.createdDateTime}
                    />
                ),
            },
            {
                ...(columnDateDefault as MRT_ColumnDef<
                    IQuoteReponse | IQuoteTempalteListResponse
                >),
                accessorKey: 'updatedDateTime',
                header: 'Ngày xóa',
                Cell: ({ row }) => (
                    <FormattedDateTimeWithFormat
                        date={(row.original as IQuoteReponse).updatedDateTime}
                    />
                ),
            },
            {
                ...(columnActionsDefault as MRT_ColumnDef<
                    IQuoteReponse | IQuoteTempalteListResponse
                >),
                Cell: ({ row }) => {
                    if (page === 'restore') {
                        return (
                            <button
                                className='btn btn-link p-0'
                                style={{ fontSize: '13px', color: '#0ab39c' }}
                                onClick={(e) => {
                                    e.stopPropagation();
                                    onRestore?.(row.original);
                                }}
                            >
                                <i className='ri-refresh-line me-1'></i>
                                Khôi phục
                            </button>
                        );
                    }
                    if (page === 'list-quote') {
                        return (
                            <div
                                onClick={(e) => {
                                    e.stopPropagation();
                                }}
                            >
                                <DropdownActionMenu
                                    actions={actions}
                                    data={row.original}
                                    direction='down'
                                    end={false}
                                />
                            </div>
                        );
                    }
                },
            },
        ],
        [actions, onRestore, page, tab],
    );

    // Bảng tab Mẫu báo giá
    const columnsTabQuotationForm = useMemo(
        () => ['name', 'quotesTypeName', 'createdDateTime', 'userNameCreated'],
        [],
    );
    // Bảng tab Báo giá liên kết
    const columnsTabLinkQuote = useMemo(
        () => [
            'quoteName',
            'dealName',
            'statusName',
            'finalTotalPrice',
            'createdDateTime',
            'userNameCreated',
        ],
        [],
    );

    // Bảng khôi phục
    const columnsRestoreQuotes = useMemo(
        () => [
            'quoteName',
            'dealName',
            'statusName',
            'finalTotalPrice',
            'updatedDateTime',
            'userNameCreated',
        ],
        [],
    );

    const columns = useMemo(() => {
        if (page === 'list-quote') {
            if (tab === TabValues.QUOTATION_FORM) {
                return allColumns.filter(
                    (column) =>
                        !column?.accessorKey ||
                        columnsTabQuotationForm.includes(
                            column?.accessorKey as string,
                        ),
                );
            }

            if (tab === TabValues.LINK_QUOTE) {
                return allColumns.filter(
                    (column) =>
                        !column?.accessorKey ||
                        columnsTabLinkQuote.includes(
                            column?.accessorKey as string,
                        ),
                );
            }
        }

        if (page === 'restore') {
            return allColumns.filter(
                (column) =>
                    !column?.accessorKey ||
                    columnsRestoreQuotes.includes(
                        column?.accessorKey as string,
                    ),
            );
        }

        return allColumns;
    }, [
        page,
        allColumns,
        tab,
        columnsTabQuotationForm,
        columnsTabLinkQuote,
        columnsRestoreQuotes,
    ]);

    return columns;
};

export default useGetColumn;
