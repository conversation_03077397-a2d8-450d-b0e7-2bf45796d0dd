export interface SearchProductGroup {
    Name?: string;
    UserNameCreated?: string;
    ParentId?: string;
    Page?: number;
    PageSize?: number;
    IsDeleted: boolean;
    FromDate?: string;
    ToDate?: string;
}

export interface ResponseSearchProductGroup {
    id: string;
    name: string;
    code: string;
    description: string;
    parentId: string;
    parentName?: string;
    userNameCreated: string;
    createdDateTime: string;
    childrens?: ResponseSearchProductGroup[];
}

export interface IProductGroup {
    id: string;
    name: string;
    code: string;
    description: string;
    parentId: string;
    userNameCreated: string;
    createdDateTime: string;
    status: string | number;
}

export interface IdsProductGroup {
    id: string[];
}

export interface Parent {
    id: string;
    name: string;
}

export interface StatusProductGroup {
    data: string;
    isError: boolean;
    errorMessage: string;
    status: number;
}
