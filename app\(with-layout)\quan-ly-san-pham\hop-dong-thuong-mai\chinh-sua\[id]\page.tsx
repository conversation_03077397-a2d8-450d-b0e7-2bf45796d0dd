'use client';
import {
    useDetailContract,
    useUpdateContract,
} from '@/apis/contracts/contracts.api';
import { IContract } from '@/apis/contracts/contracts.type';
import { ROUTES } from '@/lib/routes';
import {
    convertFormValueToPayload,
    convertPayloadToFormValue,
} from '@/utils/convert-data';
import { showToastSuccess } from '@/utils/toast-message';
import { useParams, useRouter } from 'next/navigation';
import { toast } from 'react-toastify';
import FormCreateAsic from '../../_components/Form/FormCreateAsic';

const UpdateContract = () => {
    const router = useRouter();
    const params = useParams();
    const id = params.id as string;
    const { mutate: updateContract } = useUpdateContract({
        onSuccess: () => {
            showToastSuccess({
                title: 'Cập nhật thông tin hợp đồng thương mại thành công',
                message:
                    'Thông tin hợp đồng thương mại đã được cập nhật thành công trong hệ thống.',
            });
            router.push(ROUTES.PRODUCT_MANAGEMENT.CONTRACTS.INDEX);
        },
        onError: (error) => {
            toast.error(error.message);
        },
    });

    const { data: contactDetail } = useDetailContract(id);

    const handleClose = () => {
        router.back();
    };
    const handleNewVersion = (data: IContract) => {
        const payload = convertFormValueToPayload(data, [
            'deliveryType',
            'regionDeliveryType',
            'paymentType',
            'valuePercent',
            'daysAfterSign',
            'paymentDocumentType',
            'paymentDocumentCount',
            'documentIncludedType',
            'documentQuantity',
            'deliveryWeek',
        ]);
        updateContract({ payload: payload as IContract, isMakeACopy: true });
    };
    const handleReplace = (data: IContract) => {
        const payload = convertFormValueToPayload(data, [
            'deliveryType',
            'regionDeliveryType',
            'paymentType',
            'valuePercent',
            'daysAfterSign',
            'paymentDocumentType',
            'paymentDocumentCount',
            'documentIncludedType',
            'documentQuantity',
            'deliveryWeek',
        ]);
        updateContract({ payload: payload as IContract, isMakeACopy: false });
    };
    return (
        <FormCreateAsic
            onNewVersion={handleNewVersion}
            onReplace={handleReplace}
            onClose={handleClose}
            initValue={
                convertPayloadToFormValue(contactDetail?.data) as IContract
            }
        />
    );
};

export default UpdateContract;
