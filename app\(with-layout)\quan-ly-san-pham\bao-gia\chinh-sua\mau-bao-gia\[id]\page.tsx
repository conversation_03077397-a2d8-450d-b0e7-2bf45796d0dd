'use client';

import {
    useGetQuoteTemplateDetail,
    useUpdateQuoteTemplate,
} from '@/apis/quotes/quote-templates.api';
import { IQuoteTemplateDetail } from '@/apis/quotes/quote-templates.type';
import { KEYS_TO_QUOTE_TEMPLATE } from '@/constants/key-convert';
import { ROUTES } from '@/lib/routes';
import { convertFormValueToPayload } from '@/utils/convert-data';
import { showToastError, showToastSuccess } from '@/utils/toast-message';
import { useParams, useRouter } from 'next/navigation';
import { useMemo } from 'react';
import QuotationForm from '../../../_components/QuotationForm';

const UpdateQuotationForm = () => {
    const router = useRouter();
    const params = useParams();
    const id = params.id as string;

    const { data } = useGetQuoteTemplateDetail({ id }, { enabled: !!id });

    const quoteTemplateDetail = useMemo(() => {
        if (data?.data) {
            return {
                ...data.data,
                templateType: data.data.templateType.toString(),
            };
        }

        return {};
    }, [data?.data]);

    const { mutate: updateQuoteTemplate } = useUpdateQuoteTemplate({
        onSuccess: () => {
            showToastSuccess({
                title: 'Cập nhật thông tin mẫu báo giá thành công',
                message:
                    'Thông tin mẫu báo giá đã được cập nhật thành công trong hệ thống.',
            });

            router.push(
                ROUTES.PRODUCT_MANAGEMENT.QUOTES.INDEX + '?tab=mau-bao-gia',
            );
        },
        onError: (error) => {
            showToastError({
                title: 'Cập nhật thông tin mẫu báo giá thất bại',
                message: error.detail,
            });
        },
    });

    const handleUpdateQuote = (data: IQuoteTemplateDetail) => {
        const payload = convertFormValueToPayload(data, KEYS_TO_QUOTE_TEMPLATE);

        updateQuoteTemplate(payload);
    };

    const handleCancel = () => {
        router.push(
            ROUTES.PRODUCT_MANAGEMENT.QUOTES.INDEX + '?tab=mau-bao-gia',
        );
    };
    return (
        <QuotationForm
            initValue={quoteTemplateDetail as IQuoteTemplateDetail}
            mode='update'
            onSubmit={handleUpdateQuote}
            onCancel={handleCancel}
        />
    );
};

export default UpdateQuotationForm;
