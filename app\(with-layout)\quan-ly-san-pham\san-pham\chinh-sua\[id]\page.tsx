'use client';

import { useDetailProduct, useUpdateProduct } from '@/apis/product/product.api';
import { IProduct } from '@/apis/product/product.type';
import { KEYS_TO_PRODUCT } from '@/constants/key-convert';
import { ROUTES } from '@/lib/routes';
import {
    convertFormValueToPayload,
    convertPayloadToFormValue,
} from '@/utils/convert-data';
import { useParams, useRouter } from 'next/navigation';
import { toast } from 'react-toastify';
import { Spinner } from 'reactstrap';
import FormProducts from '../../_components/FormProducts';
import { showToastSuccess } from '@/utils/toast-message';

const UpdateProducts = () => {
    const router = useRouter();
    const params = useParams();
    const id = params.id as string;
    const { data: product } = useDetailProduct(id);
    const initValue = product?.data;
    const { mutate: updateProduct } = useUpdateProduct({
        onSuccess: () => {
            showToastSuccess({
                title: 'Cập nhật thông tin sản phẩm thành công',
                message:
                    'Thông tin sản phẩm đã được cập nhật thành công trong hệ thống.',
            });
            router.push(ROUTES.PRODUCT_MANAGEMENT.PRODUCTS.INDEX);
        },
        onError: (error) => {
            toast.error(error.message);
        },
    });
    const handleSubmit = (data: IProduct) => {
        data.productOptions.map((item) => {
            item.installationType = Number(item.installationType);
            item.productOptionType = Number(item.productOptionType);
        });

        const payload = convertFormValueToPayload(data, KEYS_TO_PRODUCT);

        updateProduct(payload as IProduct);
    };
    const handleClose = () => {
        router.back();
    };
    if (!initValue) {
        return <Spinner />;
    }
    return (
        <FormProducts
            page='chinh-sua'
            initValue={convertPayloadToFormValue(initValue) as IProduct}
            onSubmit={handleSubmit}
            onClose={handleClose}
        />
    );
};
export default UpdateProducts;
