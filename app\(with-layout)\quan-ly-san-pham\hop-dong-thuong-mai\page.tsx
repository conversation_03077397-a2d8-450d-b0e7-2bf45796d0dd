'use client';
import ButtonHeader from '@/components/common/ButtonHeader';
import dynamic from 'next/dynamic';
import { useState } from 'react';

import {
    useDeleteContract,
    useGetContractNumber,
    useSearchContract,
} from '@/apis/contracts/contracts.api';
import {
    ResponseContract,
    SearchContract,
} from '@/apis/contracts/contracts.type';
import InputSearchNameWithApiControl from '@/components/common/FormController/InputSearchNameWithApiControl';
import { RootState } from '@/store/store';
import { ACTIONS } from '@/types/actions.type';
import { FormProvider, useForm, useWatch } from 'react-hook-form';
import { useSelector } from 'react-redux';
import { toast } from 'react-toastify';
import {
    Button,
    Card,
    CardHeader,
    Col,
    Container,
    Dropdown,
    DropdownToggle,
    Nav,
    NavItem,
    NavLink,
    Row,
    TabContent,
    TabPane,
} from 'reactstrap';
import { createSelector } from 'reselect';
import ContractModal from './_components/Modal/ModalContract';
import ModalVersion from './_components/Modal/ModalVersion';
import useGetColumn from './_hook/useGetColumn';

import ModalDelete from '@/components/common/Modal/ModalDelete';
import { showToastSuccess } from '@/utils/toast-message';
import ComboboxSelectUserCreate from '@/components/common/FormController/ComboboxSelectUserCreate';
const MantineTable = dynamic(
    () =>
        import('@/components/common/MantineReactTable').then((mod) => ({
            default: mod.default as typeof mod.default<ResponseContract>,
        })),
    {
        ssr: false,
    },
);

const Contracts = () => {
    const [activeTab, setActiveTab] = useState('1');
    const [selectedIds, setSelectedIds] = useState<string[]>([]);
    const [selectedNames, setSelectedNames] = useState<string[]>([]);
    const [modal, setModal] = useState(false);

    const selectAuthState = (state: RootState) => state.Auth;
    const selectAuth = createSelector(selectAuthState, (auth) => ({
        user: auth.user,
    }));
    const { user } = useSelector(selectAuth);
    const userEmail = user?.email || '';

    const methods = useForm<SearchContract>({
        defaultValues: {
            Page: 1,
            PageSize: 10,
            IsDeleted: false,
        },
    });
    const [modalCreate, setModalCreate] = useState(false);
    const toggleCreate = () => setModalCreate((prev) => !prev);
    const [modalVersion, setModalVersion] = useState(false);
    const [typeModalVersion, setTypeModalVersion] = useState('');
    const toggleVersion = () => setModalVersion((prev) => !prev);
    const [dropdownOpen, setDropdownOpen] = useState(false);
    const toggleDropdown = () => setDropdownOpen((prev) => !prev);
    const { control, setValue } = methods;
    const [
        Name,
        UserNameCreated,
        Page,
        PageSize,
        IsDeleted,
        FromDate,
        ToDate,
        ContractNumber,
    ] = useWatch({
        control,
        name: [
            'Name',
            'UserNameCreated',
            'Page',
            'PageSize',
            'IsDeleted',
            'FromDate',
            'ToDate',
            'ContractNumber',
        ],
    });
    const [contractNumber, setContractNumber] = useState('');
    const { data: listContractNumber } = useGetContractNumber(contractNumber);

    const handleSelectedAction = (action: ACTIONS, row?: ResponseContract) => {
        if (!row) {
            return;
        }

        switch (action) {
            case ACTIONS.DELETE:
                setSelectedIds([row.contractNumber]);
                setSelectedNames([row.name]);
                setModal(true);
                break;
            case ACTIONS.EDIT:
                setContractNumber(row.contractNumber);
                setTypeModalVersion('Chỉnh sửa');
                setModalVersion(true);
                break;
            case ACTIONS.VIEW_DETAIL:
                setContractNumber(row.contractNumber);
                setTypeModalVersion('Xem chi tiết');
                setModalVersion(true);
                break;
            default:
                console.error('Action not found');
                break;
        }
    };

    const columns = useGetColumn({
        onSelectedAction: handleSelectedAction,
        page: 'list',
    });
    const { data, refetch, isLoading } = useSearchContract({
        Name,
        UserNameCreated,
        Page,
        PageSize,
        IsDeleted,
        FromDate,
        ToDate,
        ContractNumber,
    });
    const { items: listContract = [], totalItems } = data ?? {};
    const { mutate: deleteContract } = useDeleteContract({
        onSuccess: () => {
            showToastSuccess({
                title: 'Xóa hợp đồng thương mại thành công',
                message:
                    'Thông tin hợp đồng thương mại đã được xóa thành công trong hệ thống.',
            });

            setModal(false);
            setSelectedNames([]);
            setSelectedIds([]);
            refetch();
        },
        onError: (error) => {
            const status = error.status;
            if (status === 400 || 401) {
                toast.warning(error.message);
                setModal(false);
                return;
            }
            toast.error(error.message);
        },
    });
    const handleConfimDelete = () => {
        deleteContract({ ids: selectedIds });
    };

    const handleClose = () => {
        setModal(false);
        setSelectedIds([]);
        setSelectedNames([]);
    };

    const renderContractTable = (isMyContracts: boolean) => {
        return (
            <>
                <CardHeader>
                    <Row>
                        <Col lg={9}>
                            <div className='d-flex gap-3 align-items-center'>
                                <InputSearchNameWithApiControl
                                    name='Name'
                                    placeholder='Tìm kiếm theo tên hợp đồng thương mại...'
                                />

                                {isMyContracts ? (
                                    ''
                                ) : (
                                    <ComboboxSelectUserCreate
                                        name='UserNameCreated'
                                        placeholder='Người tạo'
                                        style={{
                                            width: '250px',
                                        }}
                                    />
                                )}
                            </div>
                        </Col>
                        <Col lg={3}>
                            <div className='d-flex justify-content-end gap-2'>
                                {selectedIds.length > 0 ? (
                                    <Button
                                        style={{
                                            backgroundColor: 'red',
                                            border: 'none',
                                        }}
                                        onClick={() => setModal(true)}
                                    >
                                        Xóa ({selectedIds.length})
                                    </Button>
                                ) : (
                                    ''
                                )}
                                <Button
                                    outline
                                    className='filter-button'
                                    style={{
                                        border: 'none',
                                        backgroundColor: '#dff0fa',
                                    }}
                                >
                                    <i className='ri-filter-line text-primary'></i>
                                </Button>

                                <Dropdown
                                    isOpen={dropdownOpen}
                                    toggle={toggleDropdown}
                                    direction='down'
                                >
                                    <DropdownToggle
                                        outline
                                        className='settings-button'
                                        style={{
                                            border: 'none',
                                            backgroundColor: '#dff0fa',
                                        }}
                                    >
                                        <i className='ri-settings-2-line text-info'></i>
                                    </DropdownToggle>
                                    {/* <DropdownMenu>
                                        <DropdownItem>Khôi phục</DropdownItem>
                                    </DropdownMenu> */}
                                </Dropdown>
                            </div>
                        </Col>
                    </Row>
                </CardHeader>
                <MantineTable
                    columns={columns}
                    data={listContract}
                    isLoading={isLoading}
                    totalItems={totalItems ?? 0}
                    onPageChange={(page: number) => {
                        setValue('Page', page);
                    }}
                    onPageSizeChange={(size: number) => {
                        setValue('PageSize', size);
                    }}
                    tableProps={{
                        mantineSelectAllCheckboxProps: {
                            size: 'xs',
                            color: '#0ab39c',
                            style: {
                                cursor: 'pointer',
                                visibility: 'visible',
                                display: 'inline-flex',
                            },
                        },
                        mantineSelectCheckboxProps: {
                            size: 'xs',
                            color: '#0ab39c',
                            style: {
                                cursor: 'pointer',
                                visibility: 'visible',
                                display: 'inline-flex',
                            },
                        },
                        state: {
                            rowSelection: selectedIds.reduce(
                                (acc, id) => {
                                    const index = listContract.findIndex(
                                        (contact: ResponseContract) =>
                                            contact.id === id,
                                    );
                                    if (index !== -1) {
                                        acc[index] = true;
                                    }
                                    return acc;
                                },
                                {} as Record<string, boolean>,
                            ),
                            pagination: {
                                pageIndex: (Page ? Page : 1) - 1,
                                pageSize: PageSize ? PageSize : 10,
                            },
                        },
                        onRowSelectionChange: (updater) => {
                            let selectedRows: Record<string, boolean>;
                            if (typeof updater === 'function') {
                                const currentSelection = selectedIds.reduce(
                                    (acc, id) => {
                                        const index = listContract.findIndex(
                                            (contact: ResponseContract) =>
                                                contact.id === id,
                                        );
                                        if (index !== -1) {
                                            acc[index] = true;
                                        }
                                        return acc;
                                    },
                                    {} as Record<string, boolean>,
                                );
                                selectedRows = updater(currentSelection);
                            } else {
                                selectedRows = updater;
                            }

                            const newSelectedIds: string[] = [];
                            const newSelectedNames: string[] = [];
                            Object.keys(selectedRows)
                                .filter((key) => selectedRows[key])
                                .forEach((key) => {
                                    const item = listContract[parseInt(key)];
                                    if (item) {
                                        newSelectedIds.push(item.id);
                                        newSelectedNames.push(item.name);
                                    }
                                });

                            setSelectedIds(newSelectedIds);
                            setSelectedNames(newSelectedNames);
                        },
                        mantineTableBodyCellProps: {
                            align: 'left',
                        },
                    }}
                />
            </>
        );
    };

    return (
        <FormProvider {...methods}>
            <Container fluid>
                <Col md={12}>
                    <ButtonHeader
                        showDateFilters={true}
                        onCreateNew={() => setModalCreate(true)}
                    />
                </Col>

                <Col md={12}>
                    <Card>
                        <Nav tabs>
                            <NavItem>
                                <NavLink
                                    className={`px-4 py-3`}
                                    onClick={() => {
                                        setActiveTab('1');

                                        setValue('UserNameCreated', '');
                                    }}
                                    style={{
                                        cursor: 'pointer',
                                        borderRadius: '0',
                                        border: 'none',
                                        transition: 'all 0.2s ease',
                                        fontWeight: 500,
                                        marginBottom: '-1px',
                                        backgroundColor: 'transparent',
                                        borderBottom:
                                            activeTab === '1'
                                                ? '2px solid #0ab39c'
                                                : 'none',
                                        fontSize: '14px',
                                        color:
                                            activeTab === '1'
                                                ? '#0ab39c'
                                                : '#212529',
                                    }}
                                >
                                    Tất cả hợp đồng
                                </NavLink>
                            </NavItem>
                            <NavItem>
                                <NavLink
                                    className={`px-4 py-3`}
                                    onClick={() => {
                                        setActiveTab('2');
                                        setValue('UserNameCreated', userEmail);
                                    }}
                                    style={{
                                        cursor: 'pointer',
                                        borderRadius: '0',
                                        border: 'none',
                                        transition: 'all 0.2s ease',
                                        fontWeight: 500,
                                        marginBottom: '-1px',
                                        backgroundColor: 'transparent',
                                        borderBottom:
                                            activeTab === '2'
                                                ? '2px solid #0ab39c'
                                                : 'none',
                                        fontSize: '14px',
                                        color:
                                            activeTab === '2'
                                                ? '#0ab39c'
                                                : '#212529',
                                    }}
                                >
                                    Hợp đồng của tôi
                                </NavLink>
                            </NavItem>
                        </Nav>
                        <TabContent activeTab={activeTab}>
                            <TabPane tabId='1'>
                                {renderContractTable(false)}
                            </TabPane>
                            <TabPane tabId='2'>
                                {renderContractTable(true)}
                            </TabPane>
                        </TabContent>
                    </Card>
                </Col>
            </Container>
            <ContractModal isOpen={modalCreate} toggle={toggleCreate} />
            <ModalVersion
                isOpen={modalVersion}
                toggle={toggleVersion}
                title={typeModalVersion}
                versions={listContractNumber}
            />
            <ModalDelete
                onDelete={handleConfimDelete}
                onClose={handleClose}
                isOpen={modal}
                page='hợp đồng thương mại'
                data={selectedNames}
            />
        </FormProvider>
    );
};

export default Contracts;
