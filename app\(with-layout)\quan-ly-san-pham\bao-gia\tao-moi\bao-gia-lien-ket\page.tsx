'use client';

import { useCreateQuote } from '@/apis/quotes/quotes.api';
import { IQuotePayload } from '@/apis/quotes/quotes.type';
import { KEYS_TO_QUOTE } from '@/constants/key-convert';
import { ROUTES } from '@/lib/routes';
import { convertFormValueToPayload } from '@/utils/convert-data';
import { showToastError, showToastSuccess } from '@/utils/toast-message';
import { useRouter } from 'next/navigation';
import LinkQuoteForm from '../../_components/LinkQuoteForm';

const CreateLinkQuote = () => {
    const router = useRouter();

    const { mutate: createQuote, isPending } = useCreateQuote({
        onSuccess: () => {
            showToastSuccess({
                title: 'Tạo mới báo giá thành công',
                message:
                    'Thông tin báo giá đã được tạo mới thành công trong hệ thống.',
            });
            router.push(ROUTES.PRODUCT_MANAGEMENT.QUOTES.INDEX);
        },
        onError: (error) => {
            showToastError({
                title: 'Tạo mới báo giá thất bại',
                message: error.detail,
            });
        },
    });

    const handleCreateQuote = (data: IQuotePayload) => {
        if (data.customer) {
            delete data.customer;
        }
        if (data.partner) {
            delete data.partner;
        }

        if (data.expectedEndDate) {
            data.expectedEndDate = data.expectedEndDate.replace(' ', 'T');
        }

        const payload = convertFormValueToPayload(data, KEYS_TO_QUOTE);

        createQuote(payload);
    };

    const handleCancel = () => {
        router.push(ROUTES.PRODUCT_MANAGEMENT.QUOTES.INDEX);
    };

    return (
        <LinkQuoteForm
            isLoading={isPending}
            onSubmit={handleCreateQuote}
            onCancel={handleCancel}
        />
    );
};

export default CreateLinkQuote;
