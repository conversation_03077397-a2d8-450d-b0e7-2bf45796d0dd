'use client';

import { useCreatePartner } from '@/apis/partners/partners.api';
import { IPartnerPayload } from '@/apis/partners/partners.type';
import FormController from '@/components/common/FormController';
import { showToastSuccess } from '@/utils/toast-message';
import React from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { toast } from 'react-toastify';
import {
    Button,
    Col,
    Modal,
    ModalBody,
    ModalFooter,
    ModalHeader,
    Row,
} from 'reactstrap';

interface AddPartnerModalProps {
    isOpen: boolean;
    toggle: () => void;
    onSuccess?: () => void;
}

const AddPartnerModal = ({
    isOpen,
    toggle,
    onSuccess,
}: AddPartnerModalProps) => {
    const methods = useForm<IPartnerPayload>({
        defaultValues: {
            addTradePartnerDto: {
                name: '',
                shortName: '',
                email: '',
                phoneNumber: '',
                taxCode: '',
                address: '',
                addBankAccountDtos: [],
            },
            contactIds: [],
        },
    });

    const nameValue = methods.watch('addTradePartnerDto.name');

    React.useEffect(() => {
        methods.setValue('addTradePartnerDto.shortName', nameValue);
    }, [nameValue, methods]);

    const { mutate: createPartner } = useCreatePartner({
        onSuccess: () => {
            showToastSuccess({
                title: 'Tạo mới đối tác thương mại thành công',
                message:
                    'Thông tin đối tác thương mại đã được tạo mới thành công trong hệ thống.',
            });

            methods.reset();
            toggle();
            onSuccess?.();
        },
        onError: (error) => {
            toast.error(error.message);
        },
    });
    const handleSubmit = methods.handleSubmit((data: IPartnerPayload) => {
        createPartner(data);
    });

    return (
        <Modal
            isOpen={isOpen}
            toggle={toggle}
            style={{
                position: 'fixed',
                right: 0,
                top: 0,
                margin: 0,
                maxWidth: '500px',
                width: '100%',
                height: '100vh',
                transform: 'none',
            }}
            contentClassName='h-100 border-0'
            modalClassName='position-fixed end-0 top-0 h-100 m-0'
        >
            <FormProvider {...methods}>
                <ModalHeader toggle={toggle}>
                    <div
                        style={{
                            color: '#45c3b2',
                        }}
                    >
                        Tạo đối tác thương mại
                    </div>
                </ModalHeader>
                <ModalBody
                    style={{
                        flex: 1,
                        overflowY: 'auto',
                        padding: '20px',
                    }}
                >
                    <Row>
                        <Col
                            md='12'
                            style={{ marginBottom: '10px', marginTop: '10px' }}
                        >
                            <FormController
                                controlType='textInput'
                                name='addTradePartnerDto.name'
                                label='Tên đối tác thương mại'
                                placeholder='Nhập tên đối tác thương mại'
                                required={true}
                            />
                        </Col>

                        <Col
                            md='12'
                            style={{ marginBottom: '10px', marginTop: '10px' }}
                        >
                            <FormController
                                controlType='textInput'
                                name='addTradePartnerDto.taxCode'
                                label='Mã số thuế'
                                placeholder='Nhập mã số thuế'
                                required={true}
                            />
                        </Col>

                        <Col
                            md='12'
                            style={{ marginBottom: '10px', marginTop: '10px' }}
                        >
                            <FormController
                                controlType='textInput'
                                name='addTradePartnerDto.email'
                                label='Email'
                                placeholder='Nhập địa chỉ email'
                                required={true}
                            />
                        </Col>

                        <Col
                            md='12'
                            style={{ marginBottom: '10px', marginTop: '10px' }}
                        >
                            <FormController
                                controlType='textInput'
                                name='addTradePartnerDto.phoneNumber'
                                label='Số điện thoại'
                                placeholder='Nhập số điện thoại'
                                required={true}
                            />
                        </Col>
                    </Row>
                </ModalBody>
                <ModalFooter>
                    <Button
                        onClick={toggle}
                        style={{
                            backgroundColor: '#ffffff',
                            color: '#F06548',
                            borderColor: '#F06548',
                        }}
                    >
                        Hủy
                    </Button>
                    <Button
                        style={{
                            backgroundColor: '#0AB39C',
                            color: '#ffffff',
                            borderColor: '#0AB39C',
                        }}
                        onClick={handleSubmit}
                    >
                        Tạo mới
                    </Button>
                </ModalFooter>
            </FormProvider>
        </Modal>
    );
};

export default AddPartnerModal;
