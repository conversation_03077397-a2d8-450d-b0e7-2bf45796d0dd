'use client';
import {
    useRestoreCustomerGroups,
    useSearchRestoreCustomerGroups,
} from '@/apis/customer-groups/customer-groups.api';
import {
    ResponseSearchRestoresCustomerGroups,
    SearchRestoresCustomerGroups,
} from '@/apis/customer-groups/customer-groups.type';
import ButtonHeader from '@/components/common/ButtonHeader';
import InputSearchNameWithApiControl from '@/components/common/FormController/InputSearchNameWithApiControl';
import MantineTable from '@/components/common/MantineReactTable';
import { ROUTES } from '@/lib/routes';
import { showToastSuccess } from '@/utils/toast-message';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { FormProvider, useForm, useWatch } from 'react-hook-form';
import { toast } from 'react-toastify';
import { But<PERSON>, Card, CardHeader, Col, Container, Row } from 'reactstrap';
import useGetColumnRestore from '../_hook/useGetColumnRestore';

const RestoreCustomerGroup = () => {
    const router = useRouter();
    const [selectedIds, setSelectedIds] = useState<string[]>([]);
    const methods = useForm<SearchRestoresCustomerGroups>({
        defaultValues: {
            PageNumber: 1,
            PageSize: 10,
            SortField: '',
            IsDescending: false,
        },
    });
    const { control, setValue } = methods;
    const [
        Name,
        FromDate,
        ToDate,
        PageNumber,
        PageSize,
        SortField,
        IsDescending,
    ] = useWatch({
        control,
        name: [
            'Name',
            'FromDate',
            'ToDate',
            'PageNumber',
            'PageSize',
            'SortField',
            'IsDescending',
        ],
    });

    const { data, refetch, isLoading } = useSearchRestoreCustomerGroups({
        Name,
        FromDate,
        ToDate,
        PageNumber,
        PageSize,
        SortField,
        IsDescending,
    });

    const { mutate: restoreCustomerGroups } = useRestoreCustomerGroups({
        onSuccess: () => {
            showToastSuccess({
                title: 'Khôi phục nhóm khách hàng thành công',
                message:
                    'Thông tin nhóm khách hàng đã được khôi phục thành công trong hệ thống.',
            });

            refetch();
            setSelectedIds([]);
        },
        onError: () => {
            toast.error('Khôi phục nhóm khách hàng thất bại');
        },
    });
    const { items: listRestoreCustomerGroup = [], totalItems } = data ?? {};

    const handleRestoreRow = (row: ResponseSearchRestoresCustomerGroups) => {
        setSelectedIds([row.id]);
        restoreCustomerGroups({ ids: [row.id] });
    };

    const handleRestoreSelected = () => {
        restoreCustomerGroups({ ids: selectedIds });
    };
    const columns = useGetColumnRestore({
        onRestore: handleRestoreRow,
    });

    return (
        <FormProvider {...methods}>
            <Container fluid>
                <Col xl={12}>
                    <ButtonHeader
                        showCreateButton={false}
                        showImportButton={false}
                        showExportButton={false}
                        showDateFilters={true}
                    />
                </Col>
                <Col xl={12}>
                    <Card>
                        <CardHeader>
                            <Row>
                                <Col lg={9}>
                                    <div className='d-flex '>
                                        <InputSearchNameWithApiControl
                                            name='Name'
                                            placeholder='Tìm kiếm theo tên nhóm khách hàng...'
                                        />
                                    </div>
                                </Col>
                                <Col lg={3}>
                                    <div className='d-flex justify-content-end gap-2'>
                                        <Button
                                            color='success'
                                            onClick={handleRestoreSelected}
                                            disabled={selectedIds.length === 0}
                                        >
                                            {selectedIds.length > 0
                                                ? `Khôi phục (${selectedIds.length})`
                                                : 'Khôi phục'}
                                        </Button>
                                        <Button
                                            style={{
                                                backgroundColor: 'white',
                                                color: '#0ab39c',
                                                borderColor: '#0ab39c',
                                            }}
                                            onClick={() => {
                                                router.push(
                                                    ROUTES.CRM.CUSTOMER_GROUPS
                                                        .INDEX,
                                                );
                                            }}
                                        >
                                            <i className=' ri-arrow-go-back-line'></i>
                                        </Button>
                                    </div>
                                </Col>
                            </Row>
                        </CardHeader>
                        <MantineTable<ResponseSearchRestoresCustomerGroups>
                            columns={columns}
                            data={listRestoreCustomerGroup}
                            isLoading={isLoading}
                            totalItems={totalItems ?? 0}
                            onPageChange={(page: number) => {
                                setValue('PageNumber', page);
                            }}
                            onPageSizeChange={(size: number) => {
                                setValue('PageSize', size);
                            }}
                            tableProps={{
                                enableRowSelection: true,
                                enableMultiRowSelection: true,
                                initialState: {
                                    pagination: {
                                        pageIndex: (PageNumber || 1) - 1,
                                        pageSize: PageSize || 10,
                                    },
                                },
                                state: {
                                    pagination: {
                                        pageIndex: (PageNumber || 1) - 1,
                                        pageSize: PageSize || 10,
                                    },
                                    rowSelection: selectedIds.reduce(
                                        (acc, id) => {
                                            const index =
                                                listRestoreCustomerGroup.findIndex(
                                                    (
                                                        CustomerGroup: import('@/apis/customer-groups/customer-groups.type').ResponseSearchRestoresCustomerGroups,
                                                    ) =>
                                                        CustomerGroup.id === id,
                                                );
                                            if (index !== -1) {
                                                acc[index] = true;
                                            }
                                            return acc;
                                        },
                                        {} as Record<string, boolean>,
                                    ),
                                },
                            }}
                        />
                    </Card>
                </Col>
            </Container>
        </FormProvider>
    );
};
export default RestoreCustomerGroup;
