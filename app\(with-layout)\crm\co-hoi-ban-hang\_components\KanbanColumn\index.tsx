import { useSearchDeals } from '@/apis/opportunity/opportunity.api';
import {
    IDealResponse,
    IPipelineStage,
} from '@/apis/opportunity/opportunity.type';
import { formatDisplayNumber } from '@/utils/format';
import { Droppable } from '@hello-pangea/dnd';
import { useMemo, useRef } from 'react';
import { Button } from 'reactstrap';
import styles from './KanbanColumn.module.css';
import { OpportunityCard } from './OpportunityCard';

interface KanbanColumnProps {
    pipelineStage: IPipelineStage;
    opportunities?: IDealResponse[];
}

export const KanbanColumn: React.FC<KanbanColumnProps> = ({
    pipelineStage,
    opportunities: propOpportunities,
}) => {
    const { data } = useSearchDeals(
        {
            PipelineStageId: pipelineStage.id,
        },
        {
            enabled: !!pipelineStage.id,
        },
    );

    // const { columns } = useKanban();
    const columnRef = useRef<HTMLDivElement>(null);

    const opportunities = useMemo(() => {
        let result = [];

        if (Array.isArray(propOpportunities) && propOpportunities.length > 0) {
            result = propOpportunities;
        } else if (data?.items) {
            result = data.items;
        }
        return result;
    }, [data, propOpportunities]);

    // Tính tổng doanh thu
    const totalRevenue = useMemo(() => {
        const value = opportunities.reduce(
            (sum, opportunity) => sum + opportunity.amount,
            0,
        );

        return formatDisplayNumber(value);
    }, [opportunities]);

    return (
        <div ref={columnRef} className={`flex-grow-1 ${styles.kanbanColumn}`}>
            <div className='d-flex align-items-center mb-2'>
                <h6 className='text-uppercase mb-0 flex-grow-1'>
                    {pipelineStage.name}{' '}
                    <span className='badge bg-success rounded-pill ms-1'>
                        {pipelineStage.totalDeals}
                    </span>
                </h6>
                {pipelineStage.id === 'new' && (
                    <Button
                        style={{
                            backgroundColor: '#f3f3f9',
                            border: 'none',
                            padding: '4px',
                            color: '#878a99',
                        }}
                        size='sm'
                        className='btn-icon'
                    >
                        <i className='ri-add-line'></i>
                    </Button>
                )}
            </div>

            <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
                <Droppable droppableId={pipelineStage.id}>
                    {(provided, snapshot) => (
                        <div
                            ref={provided.innerRef}
                            {...provided.droppableProps}
                            className={styles.kanbanItemsContainer}
                            style={{
                                backgroundColor: snapshot.isDraggingOver
                                    ? 'rgba(0, 0, 0, 0.02)'
                                    : 'transparent',
                            }}
                        >
                            {opportunities.map((opportunity, index) => (
                                <OpportunityCard
                                    key={opportunity.dealId}
                                    opportunity={opportunity}
                                    index={index}
                                />
                            ))}
                            {provided.placeholder}
                        </div>
                    )}
                </Droppable>
                <div
                    className={`d-flex justify-content-center align-items-center gap-1 ${styles.totalRevenue}`}
                >
                    Tổng doanh thu: <strong> {totalRevenue}</strong>
                </div>
            </div>
        </div>
    );
};
