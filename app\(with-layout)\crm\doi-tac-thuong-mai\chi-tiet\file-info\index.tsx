import { IParterCompany, IPartnerContact } from '@/apis/partners/partners.type';
import BoxAddInfo from '@/components/common/BoxAddInfo';
import { ROUTES } from '@/lib/routes';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { Button, Card, CardBody, CardHeader, Col } from 'reactstrap';
import QuickViewModal from './QuickViewModal';

interface Props {
    companies: IParterCompany[];
    contacts: IPartnerContact[];
}

const FileInfo = (props: Props) => {
    const { companies = [], contacts = [] } = props;

    const router = useRouter();

    const [isQuickViewOpen, setIsQuickViewOpen] = useState(false);
    const [selectedItem, setSelectedItem] = useState<
        'customer' | 'partner' | null
    >(null);

    const toggleQuickView = (item: 'customer' | 'partner' | null = null) => {
        setIsQuickViewOpen(!isQuickViewOpen);
        setSelectedItem(item);
    };

    const customerData = {
        name: 'Gare<PERSON>',
        companyName: 'Công Ty CP Garena Việt Nam',
        email: '<EMAIL>',
        phone: '*********',
        employees: '500 nhân viên',
        website: 'www.garena.vn',
        score: '190',
        stage: 'Tiềm năng',
        status: 'Mới',
    };

    const partnerData = {
        name: 'Garena',
        companyName: 'Garena',
        email: '<EMAIL>',
        phone: '0354629272',
        employees: '500 nhân viên',
        website: 'www.garena.vn',
        score: '190',
        stage: 'Tiềm năng',
        status: 'Mới',
    };

    return (
        <Col lg={3}>
            <Card className='mb-3'>
                <CardHeader className='border-bottom-0'>
                    <div className='d-flex align-items-center'>
                        <h5 className='mb-0 flex-grow-1'>File đính kèm (2)</h5>
                        <Button
                            color='success'
                            size='sm'
                            className='btn-sm'
                            style={{
                                backgroundColor: '#0ab39c',
                                border: 'none',
                            }}
                        >
                            <i className='ri-add-line align-middle'></i> Thêm
                        </Button>
                    </div>
                </CardHeader>
                <CardBody>
                    {/* File items */}
                    {[
                        {
                            name: 'CRM.docx',
                            icon: 'ri-file-text-line',
                            date: '11/01/2025',
                        },
                        {
                            name: 'CRM.ppt',
                            icon: 'ri-file-ppt-line',
                            date: '11/01/2025',
                        },
                    ].map((file, index) => (
                        <div className='mb-3' key={index}>
                            <div className='d-flex align-items-center mb-2'>
                                <i
                                    className={`${file.icon} text-primary fs-18 me-2`}
                                ></i>
                                <div className='flex-grow-1'>
                                    <h6 className='mb-0'>{file.name}</h6>
                                    <small className='text-muted'>
                                        Ngày tải lên: {file.date}
                                    </small>
                                </div>
                                <div className='d-flex'>
                                    <Button
                                        color='light'
                                        size='sm'
                                        className='btn-icon me-1'
                                    >
                                        <i className='ri-download-line'></i>
                                    </Button>
                                    <Button
                                        color='light'
                                        size='sm'
                                        className='btn-icon'
                                    >
                                        <i className='ri-delete-bin-line'></i>
                                    </Button>
                                </div>
                            </div>
                        </div>
                    ))}

                    <Button color='info' outline block>
                        Xem thêm
                    </Button>
                </CardBody>
            </Card>

            <BoxAddInfo
                title='Doanh nghiệp'
                length={companies.length}
                content={companies.map((company) => ({
                    title: company.name,
                    data: company.associatedInfos.map((associatedInfo) => ({
                        label:
                            associatedInfo.associatedInfoTypeName === '2'
                                ? 'Email'
                                : 'Số điện thoại',
                        value: associatedInfo.value,
                        boxColor: false,
                    })),
                    onViewQuick: () => toggleQuickView('customer'),
                    onViewDetail: () => {
                        router.push(
                            ROUTES.CRM.CUSTOMERS.DETAIL.replace(
                                ':id',
                                company.id,
                            ),
                        );
                    },
                    onDelete: () => ({}),
                }))}
                // onAdd={() => {}}
            />

            <BoxAddInfo
                title='Cá nhân liên hệ'
                length={contacts.length}
                content={contacts.map((contact) => ({
                    title: contact.name,
                    data: [
                        {
                            label: 'Email',
                            value: contact.email,
                            boxColor: false,
                        },
                        {
                            label: 'Số điện thoại',
                            value: contact.phoneNumber,
                            boxColor: false,
                        },
                        {
                            label: 'Phòng ban',
                            value: contact.departmentName,
                            boxColor: false,
                        },
                        {
                            label: 'Chức vụ',
                            value: contact.positionName,
                            boxColor: false,
                        },
                        {
                            label: 'Vai trò',
                            value: contact.roleName,
                            boxColor: false,
                        },
                    ],
                    onViewQuick: () => toggleQuickView('customer'),
                    onViewDetail: () => {
                        router.push(
                            ROUTES.CRM.CUSTOMERS.DETAIL.replace(
                                ':id',
                                contact.id,
                            ),
                        );
                    },
                    onDelete: () => ({}),
                }))}
                // onAdd={() => {}}
            />

            <QuickViewModal
                isOpen={isQuickViewOpen}
                toggle={() => toggleQuickView()}
                data={selectedItem === 'customer' ? customerData : partnerData}
            />
        </Col>
    );
};

export default FileInfo;
