'use client';

import {
    useDetailSupplier,
    useUpdateSupplier,
} from '@/apis/supplier/supplier.api';
import { ISupplier } from '@/apis/supplier/supplier.type';
import { ROUTES } from '@/lib/routes';
import { showToastSuccess } from '@/utils/toast-message';
import { useParams, useRouter } from 'next/navigation';
import { toast } from 'react-toastify';
import { Spinner } from 'reactstrap';
import FormSuppliers from '../../_components/FormSuppliers';

const UpdateSuppliers = () => {
    const params = useParams();
    const router = useRouter();
    const id = params.id as string;
    const { data: dataSuppliers, isLoading } = useDetailSupplier(id);

    const { mutate: updateSupplier } = useUpdateSupplier({
        onSuccess: () => {
            showToastSuccess({
                title: 'Cập nhật thông tin nhà cung cấp thành công',
                message:
                    'Thông tin nhà cung cấp đã được cập nhật thành công trong hệ thống.',
            });
            router.push(ROUTES.PRODUCT_MANAGEMENT.SUPPLIERS.INDEX);
        },
        onError: (error) => {
            toast.error(error.message);
        },
    });

    const handleUpdate = (data: ISupplier) => {
        data.commonStatus = Number(data.commonStatus);
        updateSupplier(data);
    };

    const handleClose = () => {
        router.back();
    };

    if (isLoading) {
        return <Spinner />;
    }

    return (
        <FormSuppliers
            page='chinh-sua'
            initValue={dataSuppliers?.data}
            onSubmit={handleUpdate}
            onClose={handleClose}
        />
    );
};
export default UpdateSuppliers;
