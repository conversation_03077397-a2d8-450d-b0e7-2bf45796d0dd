'use client';

import { MantineProvider, createTheme } from '@mantine/core';
import { Notifications } from '@mantine/notifications';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactNode } from 'react';
import { Provider } from 'react-redux';
import store from '../../store/store';
import DatesProviderWrapper from './DatesProviderWrapper';
import NoSSR from './NoSSR';

const theme = createTheme({
    /** Put your mantine theme override here */
});

const queryClient = new QueryClient();

interface ClientProvidersProps {
    children: ReactNode;
}

export default function ClientProviders({ children }: ClientProvidersProps) {
    return (
        <NoSSR fallback={<div>Loading...</div>}>
            <Provider store={store}>
                <QueryClientProvider client={queryClient}>
                    <MantineProvider theme={theme}>
                        <Notifications position='top-right' zIndex={9999} />
                        <DatesProviderWrapper>{children}</DatesProviderWrapper>
                    </MantineProvider>
                </QueryClientProvider>
            </Provider>
        </NoSSR>
    );
}
