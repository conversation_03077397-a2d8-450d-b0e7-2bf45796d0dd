import { DealType } from '@/app/(with-layout)/crm/co-hoi-ban-hang/types';
import { Owner } from '@/types/user.type';
import { ContactDetail } from '../contact/contact.type';
import { IContractShorten } from '../contracts/contracts.type';
import {
    Addresses,
    AssociatedInfos,
    ICustomerShorten,
} from '../customer/customer.type';
import { IOpportunity } from '../opportunity/opportunity.type';

export interface IQuoteParams {
    Name?: string;
    FieldSearch?: string;
    UserNameCreated?: string[] | string;
    Status?: string;
    FromDate?: string;
    ToDate?: string;
    QuoteType?: string;
    PageNumber?: number;
    PageSize?: number;
    SortField?: string;
    IsDescending?: boolean;
}
export interface IQuoteReponse {
    id: string;
    // Tên mẫu báo giá
    name: string;
    quoteCode: string;
    // Tên báo giá liên kết
    quoteName: string;
    dealName: string;
    customerName: string;
    tradePartnerName: string;
    finalTotalPrice: number;
    status: number;
    statusName: string;
    expectedEndDate: string;
    createdDateTime: string;
    updatedDateTime: string;
    userNameCreated: string;
}
export interface ParamId {
    id: string;
}
export interface IProductInOppotunityResponse {
    productId: string;
    productOptionId: string;
    quantity: number;
    extendedDiscountRequest: number;
    importTaxPercent: number;
    fctTax: number;
    markUp: number;
    serviceFee: number;
}

export interface IQuotePayload {
    id?: string;
    name: string;
    quoteTemplate: string;
    requiresApproval: boolean;
    description: string;
    dealId: string;
    discountToCustomer: number;
    expectedEndDate: string;
    exchangeRate: number;
    items: IProductDisplayDeal[];
    customer?: string;
    partner?: string;
}

export interface IQuotePartner {
    email: string;
    phoneNumber: string;
    contacts: ContactDetail[];
}

export interface IQuoteDetailResponse {
    id: string;
    name: string;
    code: string;
    createdDateTime: string;
    company: ICustomerShorten;
    dealName: string;
    status: number;
    statusName: string;
    expectedEndDate: string;
    owner: Owner;
    companyInformation: {
        id: string;
        name: string;
        code: string;
        lifecycleStage: 0;
        lifecycleStageName: string;
        leadStatus: 0;
        leadStatusName: string;
        industryName: string;
        createdDateTime: string;
        addresses: Addresses[];
        associatedInfos: AssociatedInfos[];
    };
    quoteItems: IProductDisplayDeal[];
    totalNetPriceUSD: number;
    totalImportTax: number;
    amount: number;
    exchangeRate: number;
    discountToCustomer: number;
    grandTotalUSD: number;
    finalTotalPriceVND: number;
    finalTotalPriceUSD: number;
    deals: IOpportunity[];
    contracts: IContractShorten[];
    tradePartnerName: string;
    tradePartnerId: string;
    tradePartnerAvatarUrl: string;
    tradePartners: [
        {
            email: string;
            phoneNumber: string;
            contacts: [
                {
                    id: string;
                    fullName: string;
                    email: string;
                    phoneNumber: string;
                    roleName: string;
                },
            ];
        },
    ];
    requiresApproval?: boolean;
    quoteTemplate?: string;
    description?: string;
    dealId?: string;
}
export interface IQuotePayloadUpdateStatus {
    quoteId: string;
    newStatus: number;
}
export interface IProductDisplayDeal {
    productId: string;
    productType: string;
    modelCode: string;
    description: string;
    installationType: string;
    productOptionId: string;
    optionCode: string;
    listPrice: number;
    standardDiscount: number;
    serviceFee: number;
    extendedDiscountRequest: number;
    importTaxPercent: number;
    fctTax: number;
    markUp: number;
    quantity: number;
}
export interface IInformationDisplayField {
    companyName: string;
    companyId: string;
    tradePartnerName: string;
    tradePartnerId: string;
    dealType: DealType;
    dealTypeName: string;
    products: IProductDisplayDeal[];
}
export interface SearchDealId {
    id: string;
    quoteName: string;
}
