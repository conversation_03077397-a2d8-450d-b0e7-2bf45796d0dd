import http, { ApiError, ApiResponse, ApiResponseList } from '@/lib/apiBase';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import {
    IQuoteTempalteListResponse,
    IQuoteTempalteParams,
    IQuoteTemplateDetail,
    ParamId,
} from './quote-templates.type';

const URI = '/api/v1.0/QuoteTemplate';

export const quoteTemplatesKey = {
    QUOTE_TEMPLATES_LIST: 'QUOTE_TEMPLATES_LIST',
    QUOTE_TEMPLATE_DETAIL: 'QUOTE_TEMPLATE_DETAIL',
};

export const quotesUri = {
    getQuoteTemplates: `${URI}`,
    deleteQuoteTemplates: `${URI}`,
    getQuoteTemplateDetail: `${URI}/:quoteTemplateId`,
    createQuoteTemplate: `${URI}/template`,
    updateQuoteTemplate: `${URI}/template/:quoteTemplateId`,
    restoreQuoteTemplates: `${URI}/restore`,
};

export const quotesApi = {
    getQuoteTemplates: (params: IQuoteTempalteParams) => {
        return http.get<ApiResponseList<IQuoteTempalteListResponse[]>>(
            quotesUri.getQuoteTemplates,
            {
                params,
            },
        );
    },
    deleteQuoteTemplates: (ids: string[], isDeleted: boolean) => {
        return http.delete<ApiResponse<string>>(
            quotesUri.deleteQuoteTemplates,
            {
                data: ids,
                params: { isDeleted },
            },
        );
    },
    getQuoteTemplateDetail: (params: ParamId) => {
        return http.get<ApiResponse<IQuoteTemplateDetail>>(
            quotesUri.getQuoteTemplateDetail.replace(
                ':quoteTemplateId',
                params.id,
            ),
        );
    },
    createQuoteTemplate(payload: IQuoteTemplateDetail) {
        return http.post<ApiResponse<string>>(
            quotesUri.createQuoteTemplate,
            payload,
        );
    },
    updateQuoteTemplate: (payload: IQuoteTemplateDetail) => {
        const id = payload.id ?? '';
        return http.put<ApiResponse<string>>(
            quotesUri.updateQuoteTemplate.replace(':quoteTemplateId', id),
            payload,
        );
    },
    restoreQuoteTemplates: (ids: string[]) => {
        return http.post<ApiResponse<string>>(
            quotesUri.restoreQuoteTemplates,
            ids,
        );
    },
};

export const useGetQuoteTemplates = (
    params: IQuoteTempalteParams,
    o?: { enabled: boolean },
) => {
    const { enabled = true } = o ?? {};

    return useQuery({
        queryKey: [quoteTemplatesKey.QUOTE_TEMPLATES_LIST, params],
        queryFn: () => quotesApi.getQuoteTemplates(params),
        select: (data) => data,
        placeholderData: (previousData) => previousData,
        enabled,
    });
};

export const useDeleteQuoteTemplates = (props?: {
    onSuccess?: (
        data: { ids: string[]; isDeleted?: boolean },
        response: ApiResponse<string>,
    ) => void;
    onError?: (error: AxiosError<null>) => void;
}) => {
    const queryClient = useQueryClient();
    const { onSuccess, onError } = props ?? {};

    return useMutation({
        mutationFn: (params: { ids: string[]; isDeleted?: boolean }) =>
            quotesApi.deleteQuoteTemplates(params.ids, !!params?.isDeleted),
        onSuccess: (response, variables) => {
            void queryClient.invalidateQueries({
                queryKey: [quoteTemplatesKey.QUOTE_TEMPLATES_LIST],
            });
            onSuccess?.(variables, response);
        },
        onError,
    });
};

export const useGetQuoteTemplateDetail = (
    params: ParamId,
    o?: { enabled: boolean },
) => {
    const { enabled = true } = o ?? {};

    return useQuery({
        queryKey: [quoteTemplatesKey.QUOTE_TEMPLATE_DETAIL, params],
        queryFn: () => quotesApi.getQuoteTemplateDetail(params),
        select: (data) => data,
        placeholderData: (previousData) => previousData,
        enabled,
    });
};

export const useCreateQuoteTemplate = (props?: {
    onSuccess?: (
        data: IQuoteTemplateDetail,
        response: ApiResponse<string>,
    ) => void;
    onError?: (error: ApiError) => void;
}) => {
    const queryClient = useQueryClient();

    const { onSuccess, onError } = props ?? {};

    const mutation = useMutation({
        mutationFn: (payload: IQuoteTemplateDetail) =>
            quotesApi.createQuoteTemplate(payload),
        onSuccess: (response, variables) => {
            void queryClient.invalidateQueries({
                queryKey: [quoteTemplatesKey.QUOTE_TEMPLATES_LIST],
            });
            onSuccess?.(variables, response);
        },
        onError: (error: ApiError) => {
            onError?.(error);
        },
    });

    return mutation;
};

export const useUpdateQuoteTemplate = (props?: {
    onSuccess?: (
        data: IQuoteTemplateDetail,
        response: ApiResponse<string>,
    ) => void;
    onError?: (error: ApiError) => void;
}) => {
    const queryClient = useQueryClient();

    const { onSuccess, onError } = props ?? {};

    const mutation = useMutation({
        mutationFn: (payload: IQuoteTemplateDetail) =>
            quotesApi.updateQuoteTemplate(payload),
        onSuccess: (response, variables) => {
            void queryClient.invalidateQueries({
                queryKey: [quoteTemplatesKey.QUOTE_TEMPLATES_LIST],
            });
            onSuccess?.(variables, response);
        },
        onError: (error: ApiError) => {
            onError?.(error);
        },
    });

    return mutation;
};

export const useRestoreQuoteTemplates = (props?: {
    onSuccess?: (data: string[], response: ApiResponse<string>) => void;
    onError?: (error: AxiosError<null>) => void;
}) => {
    const queryClient = useQueryClient();

    const { onSuccess, onError } = props ?? {};

    const mutation = useMutation({
        mutationFn: (ids: string[]) => quotesApi.restoreQuoteTemplates(ids),
        onSuccess: (response, variables) => {
            void queryClient.invalidateQueries({
                queryKey: [quoteTemplatesKey.QUOTE_TEMPLATES_LIST],
            });
            onSuccess?.(variables, response);
        },
        onError,
    });

    return mutation;
};
