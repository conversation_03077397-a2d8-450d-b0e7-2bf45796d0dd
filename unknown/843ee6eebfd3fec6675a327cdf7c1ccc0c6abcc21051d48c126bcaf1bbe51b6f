export interface IQuoteTempalteParams {
    Name?: string;
    QuotesType?: number;
    UserNameCreated?: string;
    FromDate?: string;
    ToDate?: string;
    Page?: number;
    PageSize?: number;
    isDeleted?: boolean;
}

export interface IQuoteTempalteListResponse {
    id: string;
    name: string;
    quotesType: 1;
    quotesTypeName: string;
    userNameCreated: string;
    createdDateTime: string;
    quoteTemplateFieldsCount: 0;
}

export interface ParamId {
    id: string;
}

export interface QuoteTemplateField {
    fieldCode: string;
    fieldName: string;
    dataType: string;
    description: string;
    isVisible: boolean;
    formula: string;
    sortOrder: number;
    id?: string;
    templateId?: string;
}
export interface IQuoteTemplateDetail {
    name: string;
    description: string;
    quotesType: number;
    templateType: number;
    quoteTemplateFields: QuoteTemplateField[];
    id?: string;
    templateTypeName: string;
    userNameCreated: string;
    createdDateTime: string;
    updatedDateTime: string;
}
