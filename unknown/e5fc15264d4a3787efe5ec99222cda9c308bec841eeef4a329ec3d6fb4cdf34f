import http, { ApiError, ApiResponse, ApiResponseList } from '@/lib/apiBase';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import {
    IInformationDisplayField,
    IProductInOppotunityResponse,
    IQuoteDetailResponse,
    IQuoteParams,
    IQuotePayload,
    IQuotePayloadUpdateStatus,
    IQuoteReponse,
    ParamId,
    SearchDealId,
} from './quotes.type';

const URI = '/api/v1.0/Quotes';

export const quotesKey = {
    GET_PRODUCTS_WITH_DEAL: 'GET_PRODUCTS_WITH_DEAL',
    QUOTES_LIST: 'QUOTES_LIST',
    QUOTE_DETAIL: 'QUOTE_DETAIL',
    QUOTES_TEMPLATE_LIST: 'QUOTES_TEMPLATE_LIST',
    QUOTES_RESTORE_LIST: 'QUOTES_RESTORE_LIST',
    INFORMATION_DISPLAY_DEAL: 'INFORMATION_DISPLAY_DEAL',
    GET_QUOTE_DEAL_ID: 'GET_QUOTE_DEAL_ID',
};

export const quotesUri = {
    createQuote: `${URI}`,
    deleteQuotes: `${URI}`,
    getProductsWithDeal: `${URI}/products/:dealId`,
    getQuotes: `${URI}/search`,
    getQuoteDetail: `${URI}/:quoteId`,
    updateQuote: `${URI}/:quoteId`,
    restoreQuotes: `${URI}/restore`,
    getSearchRestoreQuotes: `${URI}/search-restore`,
    getQuotesTemplate: `${URI}/template/search`,
    updateStatusQuote: `${URI}/:quoteId/status`,
    informationDisplayDeal: `${URI}/information-display/:dealId`,
    searchQuoteByDealId: `${URI}/deal/:id`,
};

export const quotesApi = {
    createQuote(payload: IQuotePayload) {
        return http.post<ApiResponse<string>>(quotesUri.createQuote, payload);
    },
    deleteQuotes: (ids: string[], isDeleted: boolean) => {
        return http.delete<ApiResponse<string>>(quotesUri.deleteQuotes, {
            data: ids,
            params: { isDeleted },
        });
    },
    getProductsWithDeal: (params: ParamId) => {
        return http.get<ApiResponseList<IProductInOppotunityResponse[]>>(
            quotesUri.getProductsWithDeal,
            {
                params,
            },
        );
    },
    getQuotes: (params: IQuoteParams) => {
        return http.get<ApiResponse<ApiResponseList<IQuoteReponse[]>>>(
            quotesUri.getQuotes,
            {
                params,
            },
        );
    },

    getQuoteDetail: (params: ParamId) => {
        return http.get<ApiResponse<IQuoteDetailResponse>>(
            quotesUri.getQuoteDetail.replace(':quoteId', params.id),
        );
    },
    updateQuote: (payload: IQuotePayload) => {
        const id = payload.id ?? '';
        return http.put<ApiResponse<string>>(
            quotesUri.updateQuote.replace(':quoteId', id),
            payload,
        );
    },
    restoreQuotes: (ids: string[]) => {
        return http.post<ApiResponse<string>>(quotesUri.restoreQuotes, ids);
    },
    getSearchRestoreQuotes: (params: IQuoteParams) => {
        return http.get<ApiResponse<ApiResponseList<IQuoteReponse[]>>>(
            quotesUri.getSearchRestoreQuotes,
            {
                params,
            },
        );
    },
    getQuotesTemplate: (params: IQuoteParams) => {
        return http.get<ApiResponse<ApiResponseList<IQuoteReponse[]>>>(
            quotesUri.getQuotesTemplate,
            {
                params,
            },
        );
    },
    updateStatusQuote: (payload: IQuotePayloadUpdateStatus) => {
        const quoteId = payload.quoteId ?? '';
        return http.put<ApiResponse<string>>(
            quotesUri.updateQuote.replace(':quoteId', quoteId),
            payload,
        );
    },
    getInformationDisplayDeal: (params: ParamId) => {
        const dealId = params.id ?? '';
        return http.get<ApiResponse<IInformationDisplayField>>(
            quotesUri.informationDisplayDeal.replace(':dealId', dealId),
            {
                params,
            },
        );
    },
    searchQuoteByDealId: (id: string) => {
        return http.get<ApiResponse<SearchDealId[]>>(
            quotesUri.searchQuoteByDealId.replace(':id', id),
        );
    },
};

export const useCreateQuote = (props?: {
    onSuccess?: (data: IQuotePayload, response: ApiResponse<string>) => void;
    onError?: (error: ApiError) => void;
}) => {
    const queryClient = useQueryClient();

    const { onSuccess, onError } = props ?? {};

    const mutation = useMutation({
        mutationFn: (payload: IQuotePayload) => quotesApi.createQuote(payload),
        onSuccess: (response, variables) => {
            void queryClient.invalidateQueries({
                queryKey: [quotesKey.QUOTES_LIST],
            });
            onSuccess?.(variables, response);
        },
        onError: (error: ApiError) => {
            onError?.(error);
        },
    });

    return mutation;
};

export const useDeleteQuotes = (props?: {
    onSuccess?: (
        data: { ids: string[]; isDeleted?: boolean },
        response: ApiResponse<string>,
    ) => void;
    onError?: (error: AxiosError<null>) => void;
}) => {
    const queryClient = useQueryClient();
    const { onSuccess, onError } = props ?? {};

    return useMutation({
        mutationFn: (params: { ids: string[]; isDeleted?: boolean }) =>
            quotesApi.deleteQuotes(params.ids, !!params?.isDeleted),
        onSuccess: (response, variables) => {
            void queryClient.invalidateQueries({
                queryKey: [quotesKey.QUOTES_LIST],
            });
            onSuccess?.(variables, response);
        },
        onError,
    });
};

export const useGetProductsWithDeal = (params: ParamId) => {
    return useQuery({
        queryKey: [quotesKey.GET_PRODUCTS_WITH_DEAL],
        queryFn: () => quotesApi.getProductsWithDeal(params),
        select: (data) => data,
    });
};

export const useGetQuotes = (
    params: IQuoteParams,
    o?: { enabled: boolean },
) => {
    const { enabled = true } = o ?? {};

    return useQuery({
        queryKey: [quotesKey.QUOTES_LIST, params],
        queryFn: () => quotesApi.getQuotes(params),
        select: (data) => data,
        placeholderData: (previousData) => previousData,
        enabled,
    });
};

export const useGetQuoteDetail = (
    params: ParamId,
    o?: { enabled: boolean },
) => {
    const { enabled = true } = o ?? {};

    return useQuery({
        queryKey: [quotesKey.QUOTE_DETAIL, params],
        queryFn: () => quotesApi.getQuoteDetail(params),
        select: (data) => data,
        placeholderData: (previousData) => previousData,
        enabled,
    });
};

export const useUpdateQuote = (props?: {
    onSuccess?: (data: IQuotePayload, response: ApiResponse<string>) => void;
    onError?: (error: ApiError) => void;
}) => {
    const queryClient = useQueryClient();

    const { onSuccess, onError } = props ?? {};

    const mutation = useMutation({
        mutationFn: (payload: IQuotePayload) => quotesApi.updateQuote(payload),
        onSuccess: (response, variables) => {
            void queryClient.invalidateQueries({
                queryKey: [quotesKey.QUOTES_LIST],
            });
            onSuccess?.(variables, response);
        },
        onError: (error: ApiError) => {
            onError?.(error);
        },
    });

    return mutation;
};

export const useRestoreQuotes = (props?: {
    onSuccess?: (data: string[], response: ApiResponse<string>) => void;
    onError?: (error: AxiosError<null>) => void;
}) => {
    const queryClient = useQueryClient();

    const { onSuccess, onError } = props ?? {};

    const mutation = useMutation({
        mutationFn: (ids: string[]) => quotesApi.restoreQuotes(ids),
        onSuccess: (response, variables) => {
            void queryClient.invalidateQueries({
                queryKey: [quotesKey.QUOTES_LIST],
            });
            void queryClient.invalidateQueries({
                queryKey: [quotesKey.QUOTES_RESTORE_LIST],
            });
            onSuccess?.(variables, response);
        },
        onError,
    });

    return mutation;
};

export const useGetSearchRestoreQuotes = (
    params: IQuoteParams,
    o?: { enabled: boolean },
) => {
    const { enabled = true } = o ?? {};

    return useQuery({
        queryKey: [quotesKey.QUOTES_RESTORE_LIST, params],
        queryFn: () => quotesApi.getSearchRestoreQuotes(params),
        select: (data) => data,
        placeholderData: (previousData) => previousData,
        enabled,
    });
};

export const useGetQuotesTemplate = (
    params: IQuoteParams,
    o?: { enabled: boolean },
) => {
    const { enabled = true } = o ?? {};

    return useQuery({
        queryKey: [quotesKey.QUOTES_TEMPLATE_LIST, params],
        queryFn: () => quotesApi.getQuotesTemplate(params),
        select: (data) => data,
        placeholderData: (previousData) => previousData,
        enabled,
    });
};

export const useUpdateStatusQuote = (props?: {
    onSuccess?: (
        data: IQuotePayloadUpdateStatus,
        response: ApiResponse<string>,
    ) => void;
    onError?: (error: AxiosError<null>) => void;
}) => {
    const queryClient = useQueryClient();

    const { onSuccess, onError } = props ?? {};

    const mutation = useMutation({
        mutationFn: (payload: IQuotePayloadUpdateStatus) =>
            quotesApi.updateStatusQuote(payload),
        onSuccess: (response, variables) => {
            void queryClient.invalidateQueries({
                queryKey: [quotesKey.QUOTES_LIST],
            });
            onSuccess?.(variables, response);
        },
        onError,
    });

    return mutation;
};

export const useGetInformationDisplayDeal = (
    params: ParamId,
    o?: { enabled: boolean },
) => {
    const { enabled = true } = o ?? {};

    return useQuery({
        queryKey: [quotesKey.INFORMATION_DISPLAY_DEAL, params],
        queryFn: () => quotesApi.getInformationDisplayDeal(params),
        select: (data) => data.data,
        placeholderData: (previousData) => previousData,
        enabled,
    });
};
export const useSearchQuoteByDealId = (
    id: string,
    options?: { enabled?: boolean },
) => {
    const { enabled = true } = options ?? {};

    return useQuery({
        queryKey: [quotesKey.GET_QUOTE_DEAL_ID, id],
        queryFn: () => quotesApi.searchQuoteByDealId(id),
        select: (data) => data.data,
        enabled: enabled && !!id,
    });
};
