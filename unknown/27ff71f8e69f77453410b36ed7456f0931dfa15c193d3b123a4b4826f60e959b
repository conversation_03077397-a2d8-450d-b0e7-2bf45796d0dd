import React from 'react';
import { Table } from 'reactstrap';

interface WeekDay {
    date: Date;
    day: number;
    month: number;
    year: number;
    dayOfWeek: number;
}

interface Event {
    id: number;
    date: string;
    title: string;
    type: string;
    time?: string;
}

interface WeekViewProps {
    weekDays: WeekDay[];
    events: Event[];
    onDayClick?: (day: WeekDay) => void;
}

const WeekView: React.FC<WeekViewProps> = ({ weekDays, events }) => {
    const getEventsForHour = (
        hour: number,
        day: number,
        month: number,
        year: number,
    ) => {
        return events.filter((event) => {
            const [eventDay, eventMonth, eventYear] = event.date
                .split('-')
                .map(Number);
            const eventHour = event.time
                ? parseInt(event.time.split(':')[0])
                : null;
            return (
                eventDay === day &&
                eventMonth - 1 === month &&
                eventYear === year &&
                eventHour === hour
            );
        });
    };

    const hours = Array.from({ length: 12 }, (_, i) => i + 1);

    return (
        <Table
            bordered
            responsive
            className='mb-0'
            style={{ tableLayout: 'fixed' }}
        >
            <thead>
                <tr className='text-center'>
                    <th style={{ width: '100px' }}></th>
                    {weekDays.map((day) => {
                        const isToday =
                            new Date().getDate() === day.day &&
                            new Date().getMonth() === day.month &&
                            new Date().getFullYear() === day.year;
                        const dayNames = [
                            'Thứ hai',
                            'Thứ ba',
                            'Thứ tư',
                            'Thứ năm',
                            'Thứ sáu',
                            'Thứ bảy',
                            'Chủ nhật',
                        ];

                        return (
                            <th
                                key={`header-${day.day}`}
                                style={{ width: '12.85%' }}
                                className={isToday ? 'text-success' : ''}
                            >
                                <div>{dayNames[day.dayOfWeek - 1]}</div>
                                <div
                                    className={`${isToday ? 'text-success fw-bold' : ''}`}
                                >
                                    {day.day}
                                </div>
                            </th>
                        );
                    })}
                </tr>
            </thead>
            <tbody>
                {hours.map((hour) => (
                    <tr key={`hour-${hour}`}>
                        <td className='text-center align-middle'>{hour} giờ</td>
                        {weekDays.map((day) => {
                            const hourEvents = getEventsForHour(
                                hour,
                                day.day,
                                day.month,
                                day.year,
                            );

                            return (
                                <td
                                    key={`cell-${day.day}-${hour}`}
                                    style={{ height: '50px', padding: '4px' }}
                                >
                                    {hourEvents.map((event) => (
                                        <div
                                            key={event.id}
                                            className='p-1 rounded'
                                            style={{
                                                backgroundColor:
                                                    event.type === 'meeting'
                                                        ? '#cfe2ff'
                                                        : event.type ===
                                                            'report'
                                                          ? '#d1e7dd'
                                                          : '#e2f0fb',
                                                fontSize: '0.8rem',
                                                wordWrap: 'break-word',
                                                whiteSpace: 'normal',
                                                overflow: 'hidden',
                                                textOverflow: 'ellipsis',
                                                height: '100%',
                                            }}
                                        >
                                            {event.title}
                                        </div>
                                    ))}
                                </td>
                            );
                        })}
                    </tr>
                ))}
            </tbody>
        </Table>
    );
};

export default WeekView;
