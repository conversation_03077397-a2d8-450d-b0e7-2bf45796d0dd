import {
    AddressType,
    AssociatedInfoType,
    CompanyContactRole,
    FormOfPurchase,
    LeadStatusEnum,
    LifecycleStageEnum,
} from '@/constants/sharedData/sharedData.enums';

export interface SearchCustomer {
    Name?: string;
    IndustryId?: string;
    BusinessType?: string;
    LeadStatus?: number;
    SalePerson?: string;
    FromDate?: string;
    ToDate?: string;
    PageNumber?: number;
    PageSize?: number;
    SortField?: string;
    IsDescending?: boolean;
}

export interface SearchCustomerResponse {
    id: string;
    name: string;
    businessTypeName: string;
    industryName: string;
    leadStatus: string;
    salePersonName: string;
    createdOn: string;
    totalItems: number;
    totalPages: number;
    currentPage: number;
    deletedTime: string;
}

export interface ICustomer {
    id: string;
    code: string;
    name: string;
    shortName: string;
    businessTypeId: string;
    industryId: string;
    website: string;
    facebook: string;
    youtube: string;
    linkedInPage: string;
    taxCode: string;
    annualRevenue: number;
    numberOfEmployees: number;
    CustomerTypeName: string;
    industryName: string;
    leadStatus: LeadStatusEnum;
    leadStatusName: string;
    lifecycleStageEnum: LifecycleStageEnum;
    lifecycleStageEnumName: string;
    description: string;
    formOfPurchase: FormOfPurchase;
    dateOfEstablishment: number;
    ownerName: string;
    businessTypeName: string;
    formOfPurchaseName: string;
    createdDate: string;
    addresses: Addresses[];
    associatedInfoDtos: AssociatedInfoDtos[];
    addBankAccountDtos: AddBankAccountDtos[];
    contacts: Contacts[];
    tradePartners: TradePartners[];
    associatedInfos: AssociatedInfos[];
    detailTradePartnerCompanies: DetailTradePartnerCompanies[];
    companyDetailBankAccounts: CompanyDetailBankAccounts[];
    purchaseContactDetailCompanies: ContactDetail[];
    usageContactDetailCompanies: ContactDetail[];
}
export interface ContactDetail {
    id: string;
    name: string;
    email: string;
    departmentId: string;
    departmentName: string;
    positionId: string;
    positionName: string;
    phoneNumber: string;
    role: number;
    companyContactRoleName: string;
    roleName: string;
}

export interface CompanyDetailBankAccounts {
    id: string;
    bank: string;
    bankBranch: string;
    accountNumber: string;
    accountHolderName: string;
    customSwiftCode: string;
}
export interface DetailTradePartnerCompanies {
    id: string;
    email: string;
    name: string;
    phoneNumber: string;
    taxCode: string;
}
export interface AssociatedInfos {
    associatedInfoType: number;
    associatedInfoTypeName: string;
    id: string;
    value: string;
}
export interface Addresses {
    addressName: string;
    provinceId: string;
    districtId: string;
    wardId: string;
    country: string;
    addressType: AddressType;
    id: string;
    provinceName: string;
    districtName: string;
    wardName: string;
    addressTypeName: string;
    companyName: string;
}
export interface AssociatedInfoDtos {
    value: string;
    associatedInfoType: AssociatedInfoType;
}
export interface Contacts {
    contactId: string;
    role: CompanyContactRole;
}
export interface AddBankAccountDtos {
    bank: string;
    bankBranch: string;
    accountNumber: string;
    accountHolderName: string;
    customSwiftCode: string;
    companyId: string;
}
export interface TradePartners {
    tradePartnerId: string;
}

export interface DeleteCustomer {
    ids: string[];
}

export interface SearchRestores {
    Name: string;
    FromDate: string;
    ToDate: string;
    PageNumber: number;
    PageSize: number;
    SortField: string;
    IsDescending: boolean;
}
export interface ResponseSearchRestores {
    id: string;
    name: string;
    businessTypeName: string;
    industryName: string;
    leadStatus: string;
    salePersonName: string;
    deletedTime: string;
    createdOn: string;
    totalItems: number;
    totalPages: number;
    currentPage: number;
}

export interface RestoreCustomer {
    ids: string[];
}

export interface UpdateCustomerPayload {
    id: string;
    name: string;
    shortName: string;
    website: string;
    facebook: string;
    youtube: string;
    linkedInPage: string;
    taxCode: string;
    annualRevenue: number;
    numberOfEmployees: number;
    industryId: string;
    businessTypeId: string;
    leadStatus: number;
    lifecycleStageEnum: number;
    formOfPurchase: number;
    dateOfEstablishment: number;
    description: string;
    addresses: Array<{
        id: string;
        addressName: string;
        provinceId: string;
        districtId: string;
        wardId: string;
        country: string;
        addressType: number;
    }>;
    contacts: Array<{
        id: string;
        companyContactRole: number;
    }>;
    associatedInfos: Array<{
        id?: string;
        value: string;
        associatedInfoType: number;
    }>;
    bankAccountDtos: Array<{
        id: string;
        bank: string;
        bankBranch: string;
        accountNumber: string;
        accountHolderName: string;
        customSwiftCode: string;
    }>;
    companyTradePartnerDtos: Array<{
        tradePartnerId: string;
    }>;
}

export interface ICustomerShorten {
    id: string;
    name: string;
    avatarUrl: string;
}
