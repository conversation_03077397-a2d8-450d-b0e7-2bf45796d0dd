'use client';

import { useGetPipelineStages } from '@/apis/opportunity/opportunity.api';
import { IOpportunity } from '@/apis/opportunity/opportunity.type';
import CollapseApp from '@/components/common/CollapseApp';
import FormController from '@/components/common/FormController';
import ComboboxSelectCumstomerControl from '@/components/common/FormController/ComboboxSelectCumstomerControl';
import ComboboxSelectPartnerControl from '@/components/common/FormController/ComboboxSelectPartnerControl';
import ComboboxSelectUserControl from '@/components/common/FormController/ComboboxSelectUserControl';
import SelectDepartmentControl from '@/components/common/FormController/SelectDepartmentControl';
import { DateTimeFormat } from '@/constants/format';
import { Group, Radio } from '@mantine/core';
import { IconCalendar } from '@tabler/icons-react';
import { useEffect, useMemo } from 'react';
import { FormProvider, useFormContext, useWatch } from 'react-hook-form';
import { Button, Card, Col, Input, Label, Row } from 'reactstrap';
import {
    classificationNeedOptions,
    dealTypeOptions,
    PriorityOptions,
    requestDemoOptions,
} from '../../constant';
import { ClassificationNeedType, DealType } from '../../types';
import TableCompareProduct from './TableCompareProduct';
import TableInfoProduct from './TableInfoProduct';

interface FormOpportunityProps {
    onSubmit: (data: IOpportunity) => void;
    onCancel: () => void;
    nameSearchOwner?: string;
}

const FormOpportunity = (props: FormOpportunityProps) => {
    const { onSubmit, onCancel, nameSearchOwner = '' } = props;

    const methods = useFormContext<IOpportunity>();

    const { control, setValue, getValues, handleSubmit } = methods;

    const classificationNeedType = useWatch({
        control,
        name: 'customerNeed.classificationNeedType',
    });
    const [title, dealType, pipelineStageId, isCreateProject] = useWatch({
        control,
        name: ['title', 'dealType', 'pipelineStageId', 'isCreateProject'],
    });

    const { data: response } = useGetPipelineStages({
        PageNumber: 1,
        PageSize: 10,
    });

    const { items = [] } = response ?? {};

    const pipelineStages = useMemo(() => {
        if (items[0]?.pipelineStages) {
            return items[0].pipelineStages
                .filter((pipelineStage) =>
                    [1, 2, 3].includes(pipelineStage.sort),
                )
                .map((pipelineStage) => ({
                    value: pipelineStage.id,
                    label: pipelineStage.name,
                }));
        }

        return [];
    }, [items]);

    useEffect(() => {
        if (dealType === DealType.Classic.toString()) {
            setValue('customerNeed.cyberProducts', []);
        } else {
            setValue('customerNeed.classicProducts', []);
        }
    }, [dealType, setValue]);

    useEffect(() => {
        if (
            pipelineStageId === '7fa9d2ce-bef4-4bb4-6044-08ddc54ed5c2' ||
            pipelineStageId === '4c94c42a-9e93-4d42-6045-08ddc54ed5c2'
        ) {
            setValue('probability', 0);
        }
        if (pipelineStageId === 'a14cf62c-0b1a-44de-6046-08ddc54ed5c2') {
            setValue('probability', 25);
        }
    }, [pipelineStageId, setValue]);

    return (
        <FormProvider {...methods}>
            <Card
                style={{ padding: '20px 40px 20px 40px' }}
                className='d-flex flex-column gap-3'
            >
                <CollapseApp title='THÔNG TIN CHUNG'>
                    <Row className='g-3 justify-content-around'>
                        <Col md='5' className='mt-0'>
                            <FormController
                                controlType='textInput'
                                name='title'
                                label='Tên cơ hội'
                                placeholder='Nhập tên cơ hội'
                                required={true}
                            />
                        </Col>
                        <Col md='5' className='mt-0'>
                            <FormController
                                controlType='select'
                                name='dealType'
                                label='Loại cơ hội'
                                required={true}
                                placeholder='Chọn loại cơ hội...'
                                data={dealTypeOptions}
                            />
                        </Col>
                        <Col md='11'>
                            <FormController
                                controlType='textarea'
                                name='description'
                                label='Mô tả'
                                placeholder='Nhập mô tả cơ hội'
                                minRows={3}
                                autosize={true}
                            />
                        </Col>
                        <Col md='5'>
                            <FormController
                                controlType='numberInput'
                                name='amount'
                                label='Doanh thu dự kiến'
                                placeholder='Nhập doanh thu dự kiến đạt được của cơ hội này'
                                required={true}
                                thousandSeparator=','
                                decimalSeparator='.'
                                decimalScale={2}
                                step={0.01}
                            />
                        </Col>
                        <Col md='5'>
                            <FormController
                                controlType='select'
                                name='priority'
                                label='Mức độ ưu tiên'
                                placeholder='Chọn mức độ ưu tiên'
                                data={PriorityOptions}
                                required={true}
                            />
                        </Col>
                        <Col md='5'>
                            <ComboboxSelectPartnerControl
                                name='tradePartnerId'
                                label='Đối tác thương mại'
                                placeholder='Tìm kiếm đối tác thương mại'
                                style={{ width: '100%' }}
                            />
                        </Col>
                        <Col md='5'>
                            <ComboboxSelectCumstomerControl
                                name='companyId'
                                label='Khách hàng sử dụng'
                                placeholder='Tìm kiếm khách hàng'
                                style={{ width: '100%' }}
                            />
                        </Col>
                        <Col md='5'>
                            <FormController
                                controlType='select'
                                name='pipelineStageId'
                                label='Giai đoạn'
                                placeholder='Chọn giai đoạn của cơ hội này'
                                data={pipelineStages}
                                clearable
                            />
                        </Col>

                        <Col md='5'>
                            <FormController
                                controlType='numberInput'
                                name='probability'
                                label='Xác suất thắng'
                                required={true}
                                suffix='%'
                                min={0}
                                max={100}
                                step={1}
                            />
                        </Col>
                        <Col md='5'>
                            <FormController
                                controlType='select'
                                name='processesId'
                                label='Quy trình liên kết'
                                placeholder='Chọn các quy trình cần thực thi với cơ hội này'
                                data={[
                                    {
                                        label: 'Quy trình báo giá',
                                        value: '8AE1FA36-1BD2-4459-8293-CE39965D8B0C',
                                    },
                                ]}
                            />
                        </Col>
                        <Col md='5'>
                            <ComboboxSelectUserControl
                                name='ownerId'
                                label='Nhân viên kinh doanh'
                                placeholder='Chọn nhân viên kinh doanh'
                                style={{ width: '100%' }}
                                searchInit={nameSearchOwner}
                                required={true}
                            />
                        </Col>

                        {pipelineStageId ===
                            '4c94c42a-9e93-4d42-6045-08ddc54ed5c2' && (
                            <>
                                <Col md='5'>
                                    <FormController
                                        controlType='dateTimePicker'
                                        valueFormat={DateTimeFormat}
                                        name='startPoC'
                                        label='Thời gian bắt đầu POC'
                                        placeholder='Chọn thời gian bắt đầu POC'
                                        clearable
                                        leftSection={
                                            <IconCalendar
                                                size={18}
                                                stroke={1.5}
                                            />
                                        }
                                    />
                                </Col>
                                <Col md='5'>
                                    <FormController
                                        controlType='dateTimePicker'
                                        valueFormat={DateTimeFormat}
                                        name='endPoC'
                                        label='Thời gian kết thúc POC'
                                        placeholder='Chọn thời gian kết thúc POC'
                                        clearable
                                        leftSection={
                                            <IconCalendar
                                                size={18}
                                                stroke={1.5}
                                            />
                                        }
                                    />
                                </Col>
                            </>
                        )}
                    </Row>
                </CollapseApp>
                <CollapseApp title='NHU CẦU KHÁCH HÀNG'>
                    <Row className='g-3 justify-content-around'>
                        <Col md='5' className='mt-0'>
                            <FormController
                                controlType='dateTimePicker'
                                valueFormat={DateTimeFormat}
                                name='customerNeed.timeRequest'
                                label='Thời gian tiếp nhận nhu cầu'
                                placeholder='Chọn thời gian'
                                clearable
                                leftSection={
                                    <IconCalendar size={18} stroke={1.5} />
                                }
                            />
                        </Col>
                        <Col md='5' className='mt-0'>
                            <FormController
                                controlType='dateTimePicker'
                                valueFormat={DateTimeFormat}
                                name='customerNeed.estimatedPurchase'
                                label='Thời gian mua hàng dự kiến'
                                placeholder='Chọn thời gian mua hàng dự kiến'
                                clearable
                                leftSection={
                                    <IconCalendar size={18} stroke={1.5} />
                                }
                            />
                        </Col>
                        <Col md='5'>
                            <FormController
                                controlType='select'
                                name='customerNeed.classificationNeedType'
                                placeholder='Chọn phân loại nhu cầu...'
                                label='Phân loại nhu cầu'
                                data={classificationNeedOptions}
                                required={true}
                            />
                        </Col>
                        {classificationNeedType?.toString() ===
                            ClassificationNeedType.LearnProduct.toString() ||
                        classificationNeedType?.toString() ===
                            ClassificationNeedType.ProductSpecific.toString() ||
                        classificationNeedType?.toString() ===
                            ClassificationNeedType.CompareProducts.toString() ||
                        classificationNeedType?.toString() ===
                            ClassificationNeedType.CustomerCare.toString() ? (
                            <>
                                <Col md='5'>
                                    <FormController
                                        controlType='textInput'
                                        name='customerNeed.needAttention'
                                        label='Nhu cầu quan tâm'
                                        placeholder='Nhập nhu cầu của khách hàng'
                                    />
                                </Col>
                                <Col md='5'>
                                    <FormController
                                        controlType='numberInput'
                                        name='customerNeed.estimatedBudget'
                                        label='Ngân sách dự kiến'
                                        placeholder='Nhập số tiền mà khách hàng dự định sẽ trả'
                                        thousandSeparator=','
                                        decimalSeparator='.'
                                        decimalScale={2}
                                        step={0.01}
                                    />
                                </Col>
                                {classificationNeedType?.toString() !==
                                ClassificationNeedType.LearnProduct.toString() ? (
                                    <Col md='5'>
                                        <FormController
                                            controlType='select'
                                            name='customerNeed.requestDemo'
                                            label='Yêu cầu demo/ PoC'
                                            placeholder='Khách hàng có muốn thử nghiệm giải pháp trước không?'
                                            data={requestDemoOptions}
                                        />
                                    </Col>
                                ) : (
                                    <Col md='5'></Col>
                                )}
                                <Col md='11'>
                                    <FormController
                                        controlType='textarea'
                                        name='customerNeed.contentExchange'
                                        label='Nội dung trao đổi'
                                        placeholder='Nhập nhu cầu của khách hàng'
                                        minRows={3}
                                    />
                                </Col>
                                <Col md='11'>
                                    {dealType ===
                                    DealType.Classic.toString() ? (
                                        <TableInfoProduct />
                                    ) : (
                                        <TableCompareProduct />
                                    )}
                                </Col>
                            </>
                        ) : (
                            <Col md='5'></Col>
                        )}
                    </Row>
                </CollapseApp>

                <div className='mb-3 d-flex align-items-center gap-2'>
                    <Input
                        type='checkbox'
                        style={{ width: '20px', height: '20px' }}
                        onChange={(event) => {
                            const value = event.target.checked as boolean;

                            setValue('isCreateProject', value);
                            if (value) {
                                setValue(
                                    'project.name',
                                    `Dự án ${title ?? ''}`,
                                );
                            }
                        }}
                        checked={!!getValues('isCreateProject')}
                    />{' '}
                    <p
                        className='mb-0'
                        style={{ fontSize: '14px', fontWeight: 'bold' }}
                    >
                        TẠO DỰ ÁN
                    </p>
                </div>
                {isCreateProject && (
                    <Row className='g-3 justify-content-around'>
                        <Col md='11'>
                            <FormController
                                controlType='textInput'
                                name='project.name'
                                label='Tên dự án'
                                placeholder='Nhập tên dự án'
                                required={true}
                            />
                        </Col>
                        <Col md='11'>
                            <FormController
                                controlType='textarea'
                                name='project.description'
                                label='Mô tả dự án'
                                placeholder='Nhập mô tả dự án'
                                minRows={3}
                            />
                        </Col>
                        <Col md='11'>
                            <Label className='d-block mb-2'>Loại địa chỉ</Label>
                            <Radio.Group
                                name='project.type'
                                // onChange={(value) =>
                                //     setValue('project.type', value)
                                // }
                            >
                                <Group mt='xs'>
                                    {[
                                        { value: 1, label: 'Tạo dự án mới' },
                                        { value: 2, label: 'Tạo từ dự án mẫu' },
                                    ].map((type, index) => (
                                        <Radio
                                            key={`project-type-${index}`}
                                            value={type.value}
                                            label={type.label}
                                        />
                                    ))}
                                </Group>
                            </Radio.Group>
                        </Col>
                        <Col md='5'>
                            <ComboboxSelectUserControl
                                name='project.managerId'
                                label='Người quản lý'
                                placeholder='Chọn người quản lý dự án'
                                style={{ width: '100%' }}
                                required={true}
                            />
                        </Col>
                        <Col md='5'>
                            <SelectDepartmentControl
                                name='project.departmentId'
                                label='Phòng ban'
                                placeholder='Chọn phòng ban quản lý dự án'
                                required={true}
                                style={{ width: '100%' }}
                            />
                        </Col>
                        <Col md='5'>
                            <ComboboxSelectUserControl
                                name='project.watcherUserIds'
                                label='Người theo dõi'
                                placeholder='Chọn người theo dõi dự án'
                                style={{ width: '100%' }}
                                required={true}
                            />
                        </Col>
                        <Col md='5'></Col>
                    </Row>
                )}

                <div className='d-flex justify-content-end mt-4'>
                    <Button
                        color='danger'
                        className='me-2'
                        type='button'
                        onClick={onCancel}
                    >
                        Hủy
                    </Button>
                    <Button
                        color='success'
                        type='button'
                        onClick={handleSubmit(onSubmit)}
                    >
                        Tạo mới
                    </Button>
                </div>
            </Card>
        </FormProvider>
    );
};

export default FormOpportunity;
