import { useFormContext } from 'react-hook-form';
import CollapseApp from '@/components/common/CollapseApp';
import FormController from '@/components/common/FormController';
import { Row, Col } from 'reactstrap';
import { IconCalendar } from '@tabler/icons-react';

const InfoContract = () => {
    useFormContext();
    return (
        <CollapseApp title='THÔNG TIN HỢP ĐỒNG'>
            <div style={{ padding: '20px 40px 20px 40px' }}>
                <Row>
                    <Col
                        md={6}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <FormController
                            controlType='textInput'
                            name='contractNumber'
                            label='Số hợp đồng'
                            placeholder='Nhập số hợp đồng'
                            required={true}
                        />
                    </Col>
                    <Col
                        md={6}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <FormController
                            controlType='dateTimePicker'
                            name='signDate'
                            label='<PERSON><PERSON><PERSON> ký hợp đồng'
                            placeholder='Chọn ngày ký hợp đồng...'
                            required={true}
                            clearable
                            style={{ width: '100%' }}
                            leftSection={
                                <IconCalendar size={18} stroke={1.5} />
                            }
                        />
                    </Col>
                </Row>
            </div>
        </CollapseApp>
    );
};
export default InfoContract;
